"""
旅行规划API路由

提供旅行规划相关的RESTful API和SSE流式接口。
"""
import uuid
import asyncio
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_models import TravelRequest, TravelItinerary
from src.memory.memory_manager import memory_manager
from src.tools.sse_output import sse_manager
from src.core.logger import get_logger

logger = get_logger('api.travel_planner')

router = APIRouter(prefix="/travel-planner", tags=["Travel Planner"])


class PlanningRequest(BaseModel):
    """旅行规划请求模型"""
    user_id: str = Field(..., description="用户ID")
    query: str = Field(..., description="旅行规划查询")
    session_id: Optional[str] = Field(None, description="会话ID")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="上下文信息")


class PlanningResponse(BaseModel):
    """旅行规划响应模型"""
    success: bool
    trace_id: str
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.post("/plan", response_model=PlanningResponse)
async def create_travel_plan(
    request: PlanningRequest,
    background_tasks: BackgroundTasks
) -> PlanningResponse:
    """
    创建旅行规划（同步接口）
    """
    try:
        # 创建Agent实例
        agent = TravelPlannerAgent()
        
        # 初始化状态
        state = agent.initialize_state(
            user_id=request.user_id,
            session_id=request.session_id
        )
        
        logger.info(f"Starting travel planning for user {request.user_id}, trace_id: {state.trace_id}")
        
        # 处理规划请求
        result = await agent.process(request.query, request.context)
        
        # 后台保存行程到数据库
        if result.get('status') == 'success' and 'itinerary' in result:
            background_tasks.add_task(
                save_itinerary_to_db,
                result['itinerary'],
                state.trace_id
            )
        
        return PlanningResponse(
            success=True,
            trace_id=state.trace_id,
            message="Travel plan created successfully",
            data=result
        )
        
    except Exception as e:
        logger.error(f"Failed to create travel plan: {str(e)}")
        return PlanningResponse(
            success=False,
            trace_id=str(uuid.uuid4()),
            message="Failed to create travel plan",
            error=str(e)
        )


@router.get("/plan/stream/{session_id}")
async def stream_travel_plan(session_id: str):
    """
    流式旅行规划接口（SSE）
    """
    try:
        logger.info(f"Starting SSE stream for session: {session_id}")
        
        return StreamingResponse(
            sse_manager.stream_events(session_id),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to start SSE stream: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/plan/stream", response_model=PlanningResponse)
async def start_stream_planning(
    request: PlanningRequest,
    background_tasks: BackgroundTasks
) -> PlanningResponse:
    """
    启动流式旅行规划
    """
    try:
        # 生成会话ID（如果没有提供）
        session_id = request.session_id or str(uuid.uuid4())
        
        # 启动后台规划任务
        background_tasks.add_task(
            execute_stream_planning,
            request.user_id,
            request.query,
            session_id,
            request.context
        )
        
        return PlanningResponse(
            success=True,
            trace_id=session_id,
            message="Stream planning started",
            data={"session_id": session_id}
        )
        
    except Exception as e:
        logger.error(f"Failed to start stream planning: {str(e)}")
        return PlanningResponse(
            success=False,
            trace_id=str(uuid.uuid4()),
            message="Failed to start stream planning",
            error=str(e)
        )


@router.get("/history/{user_id}")
async def get_user_travel_history(
    user_id: str,
    limit: int = 10
) -> Dict[str, Any]:
    """
    获取用户旅行历史
    """
    try:
        itineraries = await memory_manager.get_user_itineraries(user_id, limit)
        
        return {
            "success": True,
            "user_id": user_id,
            "itineraries": itineraries,
            "count": len(itineraries)
        }
        
    except Exception as e:
        logger.error(f"Failed to get travel history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/plan/{trace_id}")
async def get_travel_plan(trace_id: str) -> Dict[str, Any]:
    """
    根据trace_id获取旅行规划
    """
    try:
        itinerary = await memory_manager.get_itinerary_by_trace_id(trace_id)
        
        if not itinerary:
            raise HTTPException(status_code=404, detail="Travel plan not found")
        
        return {
            "success": True,
            "itinerary": itinerary
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get travel plan: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user/{user_id}/profile")
async def get_user_profile(user_id: str) -> Dict[str, Any]:
    """
    获取用户画像
    """
    try:
        user = await memory_manager.get_user_profile(user_id)
        
        if not user:
            return {
                "success": False,
                "message": "User profile not found",
                "profile": None
            }
        
        return {
            "success": True,
            "profile": user.model_dump()
        }
        
    except Exception as e:
        logger.error(f"Failed to get user profile: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user/{user_id}/statistics")
async def get_user_statistics(user_id: str) -> Dict[str, Any]:
    """
    获取用户统计信息
    """
    try:
        stats = await memory_manager.get_user_statistics(user_id)
        
        return {
            "success": True,
            "user_id": user_id,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get user statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 后台任务函数 ====================

async def execute_stream_planning(
    user_id: str,
    query: str,
    session_id: str,
    context: Dict[str, Any]
):
    """执行流式规划的后台任务"""
    try:
        # 创建Agent实例
        agent = TravelPlannerAgent()
        
        # 初始化状态
        state = agent.initialize_state(user_id=user_id, session_id=session_id)
        
        logger.info(f"Executing stream planning for session: {session_id}")
        
        # 流式处理
        final_result = None
        async for result in agent.stream_process(query, context):
            # 结果会通过SSE自动发送给客户端
            if result.get('step') == 'completed':
                final_result = result.get('data')
        
        # 保存最终结果到数据库
        if final_result and 'itinerary' in final_result:
            await save_itinerary_to_db(final_result['itinerary'], state.trace_id)
        
        # 清理Agent资源
        await agent.cleanup()
        
    except Exception as e:
        logger.error(f"Stream planning failed for session {session_id}: {str(e)}")
        # 发送错误消息
        await sse_manager.send_error(session_id, str(e))


async def save_itinerary_to_db(itinerary_data: Dict[str, Any], trace_id: str):
    """保存行程到数据库的后台任务"""
    try:
        # 构建TravelItinerary对象
        itinerary = TravelItinerary(**itinerary_data)
        itinerary.trace_id = trace_id
        
        # 保存到数据库
        success = await memory_manager.save_itinerary(itinerary)
        
        if success:
            logger.info(f"Itinerary saved to database: {trace_id}")
        else:
            logger.error(f"Failed to save itinerary to database: {trace_id}")
            
    except Exception as e:
        logger.error(f"Error saving itinerary to database: {str(e)}")
