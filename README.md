# AutoPilot AI - 智能旅行规划助手

基于高德地图API和大语言模型的智能旅行规划系统，提供个性化的旅行路线规划和实时推荐服务。

## 🌟 主要特性

- **智能路线规划**: 基于高德地图API的精准路线规划
- **个性化推荐**: 根据用户画像和历史记录提供定制化建议
- **实时流式输出**: 通过SSE技术提供实时的思考过程和执行进度
- **原子化能力**: 16个核心高德地图工具，支持地点搜索、路径规划、周边服务等
- **记忆系统**: MongoDB存储用户画像、行程记录和执行日志
- **现代化界面**: 响应式HTML前端，支持实时进度显示和结果呈现

## 🏗️ 系统架构

```
├── src/
│   ├── agents/              # 智能体实现
│   │   ├── base_agent.py    # 基础Agent类
│   │   └── travel_planner_agent.py  # 旅行规划Agent
│   ├── api/                 # API接口层
│   │   └── travel_planner/  # 旅行规划API
│   ├── core/                # 核心模块
│   │   ├── config.py        # 配置管理
│   │   ├── database.py      # 数据库连接
│   │   ├── llm_manager.py   # LLM管理器
│   │   └── logger.py        # 日志系统
│   ├── memory/              # 记忆系统
│   │   └── memory_manager.py # 记忆管理器
│   ├── models/              # 数据模型
│   │   └── travel_models.py # 旅行相关模型
│   ├── tools/               # 工具集成
│   │   ├── registry.py      # 工具注册器
│   │   ├── sse_output.py    # SSE输出管理
│   │   ├── amap_tools.py    # 高德地图工具
│   │   └── memory_tools.py  # 记忆工具
│   └── main.py              # 应用入口
├── static/                  # 前端静态文件
│   ├── index.html          # 主页面
│   ├── css/style.css       # 样式文件
│   └── js/app.js           # JavaScript应用
├── tests/                   # 测试用例
├── config/                  # 配置文件
└── docs/                    # 文档
```

## 🚀 快速开始

### 环境要求

- Python 3.12+
- MongoDB 4.4+
- 高德地图API密钥

### 安装步骤

1. **激活虚拟环境**
```bash
# Windows
venv\Scripts\activate
```

2. **安装依赖**
```bash
pip install fastapi uvicorn motor pydantic pydantic-settings aiohttp asyncio
```

3. **配置环境**
编辑 `config/default.yaml` 文件，配置必要的API密钥和数据库连接信息。

4. **初始化开发环境**
```bash
python run.py setup
```

5. **运行健康检查**
```bash
python run.py health
```

6. **启动应用**
```bash
python run.py server
```

应用将在 http://localhost:8000 启动，前端界面访问 http://localhost:8000/static/index.html

## 📖 使用指南

### Web界面使用

1. 打开浏览器访问 http://localhost:8000/static/index.html
2. 输入用户ID（用于个性化推荐）
3. 在文本框中描述您的旅行需求，例如：
   - "我想去北京玩3天，喜欢历史古迹和美食，预算中等"
   - "计划上海2日游，偏爱现代建筑和购物，预算充足"
4. 选择是否启用实时流式规划
5. 点击"开始规划"按钮
6. 查看实时规划过程和最终结果

### API接口使用

#### 创建旅行规划
```bash
curl -X POST "http://localhost:8000/travel-planner/plan" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "query": "我想去北京玩3天，喜欢历史古迹和美食",
    "context": {}
  }'
```

#### 流式规划
```bash
# 启动流式规划
curl -X POST "http://localhost:8000/travel-planner/plan/stream" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "query": "我想去上海玩2天",
    "session_id": "session_001"
  }'

# 连接SSE流
curl "http://localhost:8000/travel-planner/plan/stream/session_001"
```

#### 获取历史记录
```bash
curl "http://localhost:8000/travel-planner/history/user_001"
```

## 🏗️ 项目架构

```
autopilotai/
├── src/                    # 主要源码
│   ├── core/              # 核心模块
│   │   ├── config.py      # 配置管理
│   │   ├── logger.py      # 日志系统
│   │   └── llm_manager.py # LLM管理器
│   └── agents/            # Agent实现
│       ├── simple_assistant.py    # 基础助手
│       └── python_expert.py       # Python专家
├── tests/                 # 测试套件
│   ├── unit/             # 单元测试
│   ├── integration/      # 集成测试
│   └── scripts/          # 测试脚本
├── config/               # 配置文件
└── doc/                  # 项目文档
```

## 🔧 支持的模型

### 智谱AI模型
- **glm-4-flash**：基础对话模型，响应快速，适合日常交互
- **glm-z1-flash**：推理模型，支持复杂思考和分析

### OpenAI模型（计划支持）
- GPT-4、GPT-3.5-turbo等（通过配置适配）

## 🧪 测试体系

### 测试覆盖
- **单元测试**：39+ 个测试用例，覆盖核心模块
- **集成测试**：端到端流程验证
- **AutoGen适配器测试**：17个Mock测试，确保集成稳定

### 测试命令
```bash
# 完整测试套件
.\tests\scripts\test_all_agents.ps1

# 指定测试类型
.\tests\scripts\test_all_agents.ps1 -UnitOnly    # 仅单元测试
.\tests\scripts\test_all_agents.ps1 -AutoGen     # 仅AutoGen测试
.\tests\scripts\test_all_agents.ps1 -Quick       # 快速测试

# 覆盖率测试
pytest tests/unit/ --cov=src --cov-report=html
```

## 📖 文档

- [快速开始指南](AUTOGEN_QUICKSTART.md) - AutoGen集成使用指南
- [测试指南](doc/测试指南.md) - 详细的测试说明
- [Agent技术规范](doc/Agent技术实现规范.md) - Agent开发规范
- [项目进度](doc/进度文档.md) - 开发进度记录

## 🛠️ 开发

### 开发环境
```bash
# 安装开发依赖
pip install -e ".[dev]"

# 代码格式化
black src/ tests/

# 类型检查
mypy src/

# 运行测试
pytest tests/unit/ -v
```

### 贡献指南
1. Fork项目
2. 创建特性分支
3. 编写测试用例
4. 确保所有测试通过
5. 提交Pull Request

## 📊 项目状态

| 模块 | 状态 | 测试覆盖率 | 说明 |
|------|------|------------|------|
| 配置管理 | ✅ 完成 | 100% | 支持多环境配置 |
| 日志系统 | ✅ 完成 | 99% | 结构化日志、分布式追踪 |
| LLM管理器 | ✅ 完成 | 84% | 支持多种LLM后端 |
| 基础Agent | ✅ 完成 | 85%+ | 对话管理、状态维护 |
| AutoGen集成 | ✅ 完成 | 95%+ | 完全兼容AutoGen框架 |

## 🔮 规划路线

### 已完成（阶段一）
- ✅ 核心框架（配置、日志、LLM管理）
- ✅ 基础Agent系统
- ✅ AutoGen框架集成
- ✅ 完整测试体系

### 进行中（阶段二）
- 🔄 工具注册表系统
- 🔄 本地工具集成（搜索、文件操作等）
- 🔄 ExecutorAgent实现

### 计划中（阶段三）
- 📋 多Agent协作工作流
- 📋 Web界面和API服务
- 📋 插件系统和生态

## 📞 支持

如有问题或建议：
1. 查看[文档](doc/)了解详细信息
2. 运行诊断测试检查环境
3. 提交Issue或联系开发团队

## 📄 许可证

[待添加许可证信息]

---

**AutoPilot AI** - 让AI Agent开发更简单、更强大