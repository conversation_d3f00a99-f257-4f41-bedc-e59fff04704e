#!/usr/bin/env python3
"""
应用启动脚本

提供便捷的应用启动方式，支持不同的运行模式。
"""
import sys
import os
import asyncio
import argparse
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.logger import get_logger
from src.core.database import db_manager

logger = get_logger('run')


async def check_dependencies():
    """检查依赖和配置"""
    logger.info("Checking dependencies and configuration...")
    
    # 检查配置文件
    config_file = project_root / "config" / "default.yaml"
    if not config_file.exists():
        logger.error("Configuration file not found: config/default.yaml")
        return False
    
    # 检查静态文件
    static_dir = project_root / "static"
    if not static_dir.exists():
        logger.error("Static directory not found: static/")
        return False
    
    # 检查数据库连接
    try:
        await db_manager.initialize()
        health = await db_manager.health_check()
        
        if not health.get("mongodb", False):
            logger.warning("MongoDB connection failed - some features may not work")
        else:
            logger.info("Database connections verified")
        
        await db_manager.cleanup()
        
    except Exception as e:
        logger.warning(f"Database check failed: {str(e)} - continuing anyway")
    
    logger.info("Dependency check completed")
    return True


def run_server(host="0.0.0.0", port=8000, reload=True, log_level="info"):
    """运行FastAPI服务器"""
    logger.info(f"Starting server on {host}:{port}")
    
    uvicorn.run(
        "src.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level=log_level,
        access_log=True
    )


def run_tests():
    """运行测试"""
    import subprocess
    
    logger.info("Running tests...")
    
    try:
        # 运行pytest
        result = subprocess.run([
            sys.executable, "-m", "pytest",
            "tests/",
            "-v",
            "--tb=short",
            "--disable-warnings"
        ], cwd=project_root)
        
        if result.returncode == 0:
            logger.info("All tests passed!")
        else:
            logger.error("Some tests failed!")
        
        return result.returncode == 0
        
    except Exception as e:
        logger.error(f"Failed to run tests: {str(e)}")
        return False


def run_dev_setup():
    """开发环境设置"""
    logger.info("Setting up development environment...")
    
    # 创建必要的目录
    directories = [
        "logs",
        "static/uploads",
        "tests/fixtures"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")
    
    # 检查虚拟环境
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        logger.info("Virtual environment detected")
    else:
        logger.warning("No virtual environment detected - consider using venv")
    
    logger.info("Development setup completed")


async def run_health_check():
    """运行健康检查"""
    logger.info("Running health check...")
    
    try:
        # 检查数据库
        await db_manager.initialize()
        health = await db_manager.health_check()
        
        logger.info("Health check results:")
        for service, status in health.items():
            status_text = "✓ OK" if status else "✗ FAILED"
            logger.info(f"  {service}: {status_text}")
        
        await db_manager.cleanup()
        
        # 检查工具注册
        from src.tools.registry import tool_registry
        import src.tools.amap_tools
        import src.tools.memory_tools
        
        tools_count = len(tool_registry.list_tools())
        logger.info(f"  Tools registered: {tools_count}")
        
        return all(health.values())
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AutoPilot AI Travel Planner")
    parser.add_argument("command", choices=["server", "test", "setup", "health"], 
                       help="Command to run")
    parser.add_argument("--host", default="0.0.0.0", help="Server host")
    parser.add_argument("--port", type=int, default=8000, help="Server port")
    parser.add_argument("--no-reload", action="store_true", help="Disable auto-reload")
    parser.add_argument("--log-level", default="info", 
                       choices=["debug", "info", "warning", "error"],
                       help="Log level")
    
    args = parser.parse_args()
    
    if args.command == "server":
        # 运行依赖检查
        if not asyncio.run(check_dependencies()):
            logger.error("Dependency check failed")
            sys.exit(1)
        
        # 启动服务器
        run_server(
            host=args.host,
            port=args.port,
            reload=not args.no_reload,
            log_level=args.log_level
        )
    
    elif args.command == "test":
        # 运行测试
        success = run_tests()
        sys.exit(0 if success else 1)
    
    elif args.command == "setup":
        # 开发环境设置
        run_dev_setup()
        
        # 运行依赖检查
        if asyncio.run(check_dependencies()):
            logger.info("Setup completed successfully!")
        else:
            logger.error("Setup completed with warnings")
            sys.exit(1)
    
    elif args.command == "health":
        # 健康检查
        success = asyncio.run(run_health_check())
        if success:
            logger.info("Health check passed!")
            sys.exit(0)
        else:
            logger.error("Health check failed!")
            sys.exit(1)


if __name__ == "__main__":
    main()
