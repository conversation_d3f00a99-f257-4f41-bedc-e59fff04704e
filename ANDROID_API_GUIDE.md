# AutoPilot AI - Android API 后端指南

本指南专门为Android应用开发者提供后端API的配置和使用说明。

## 🎯 概述

AutoPilot AI 提供独立的后端API服务，支持Android应用调用。后端可以在有或没有数据库的情况下运行，提供灵活的部署选项。

## 🚀 快速启动

### 方式一：使用专用API服务器（推荐）

```bash
# 1. 激活虚拟环境
venv\Scripts\activate

# 2. 安装基础依赖
pip install fastapi uvicorn aiohttp

# 3. 启动API服务器
python start_api_server.py
```

### 方式二：使用完整应用

```bash
# 启动完整应用（需要配置数据库）
python run.py server
```

## 📊 数据库配置

您的数据库配置已经在 `config/default.yaml` 中设置：

```yaml
# MongoDB配置
mongodb:
  host: "***********"
  port: 27017
  username: "admin"
  password: "m25uids*g"
  database: "dh_platform_data"

# MySQL配置  
mysql:
  host: "***********"
  port: 19090
  username: "root"
  password: "Fsit#2024"

# Redis配置
redis:
  host: "***********"
  port: 5182
  password: "kLKe3NFM4RZMgXhA"
```

## 🔧 API端点

### 核心端点

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/` | 根端点，返回API基本信息 |
| GET | `/health` | 健康检查 |
| POST | `/travel-planner/plan` | 创建旅行规划 |
| GET | `/travel-planner/history/{user_id}` | 获取用户历史记录 |
| GET | `/docs` | API文档 (Swagger UI) |

### 旅行规划API

**请求示例：**
```json
POST /travel-planner/plan
{
  "user_id": "android_user_001",
  "query": "我想去北京玩3天，喜欢历史古迹和美食",
  "context": {
    "platform": "android",
    "version": "1.0.0"
  }
}
```

**响应示例：**
```json
{
  "success": true,
  "trace_id": "trace_android_user_001_1234",
  "data": {
    "itinerary": {
      "user_id": "android_user_001",
      "summary": {
        "title": "北京3日历史文化美食之旅",
        "suggestion": "精心规划的北京深度游",
        "duration_days": 3,
        "budget_estimate": "中等预算"
      },
      "daily_plans": [
        {
          "day_number": 1,
          "theme": "皇城文化探索",
          "pois": [
            {
              "name": "故宫博物院",
              "address": "北京市东城区景山前街4号",
              "type": "历史古迹",
              "rating": 4.6,
              "description": "明清两朝的皇家宫殿"
            }
          ]
        }
      ]
    }
  }
}
```

## 📱 Android集成示例

### Java/Kotlin HTTP请求

```java
public class TravelPlannerAPI {
    private static final String BASE_URL = "http://your-server:8000";
    private OkHttpClient client = new OkHttpClient();
    
    public void planTravel(String userId, String query, Callback callback) {
        JSONObject request = new JSONObject();
        try {
            request.put("user_id", userId);
            request.put("query", query);
            
            JSONObject context = new JSONObject();
            context.put("platform", "android");
            context.put("version", "1.0.0");
            request.put("context", context);
            
            RequestBody body = RequestBody.create(
                request.toString(), 
                MediaType.parse("application/json")
            );
            
            Request httpRequest = new Request.Builder()
                .url(BASE_URL + "/travel-planner/plan")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();
                
            client.newCall(httpRequest).enqueue(callback);
            
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
```

### Retrofit接口定义

```java
public interface TravelPlannerService {
    @POST("travel-planner/plan")
    Call<TravelPlanResponse> createTravelPlan(@Body TravelPlanRequest request);
    
    @GET("travel-planner/history/{userId}")
    Call<UserHistoryResponse> getUserHistory(@Path("userId") String userId);
    
    @GET("health")
    Call<HealthResponse> healthCheck();
}
```

## 🧪 测试API

### 使用测试脚本

```bash
# 测试所有API端点
python test_api.py
```

### 使用cURL

```bash
# 健康检查
curl http://localhost:8000/health

# 创建旅行规划
curl -X POST "http://localhost:8000/travel-planner/plan" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test_user",
    "query": "我想去上海玩2天",
    "context": {"platform": "android"}
  }'
```

## 🔒 安全配置

### CORS设置

API默认允许所有来源的跨域请求。生产环境中建议限制：

```python
# 在配置中设置允许的域名
allowed_origins = [
    "https://yourdomain.com",
    "http://localhost:3000"  # 开发环境
]
```

### API密钥认证（可选）

如需要API密钥认证，在请求头中添加：

```
Authorization: Bearer your_api_key_here
```

## 🚀 部署选项

### 1. 基础模式（无数据库）

适合快速测试和演示：

```bash
python start_api_server.py
# 选择 "2. 基础模式"
```

特点：
- 无需数据库配置
- 提供模拟响应
- 快速启动
- 适合开发测试

### 2. 完整模式（含数据库）

适合生产环境：

```bash
python start_api_server.py
# 选择 "1. 完整模式"
```

特点：
- 完整功能支持
- 数据持久化
- 用户画像管理
- 历史记录存储

### 3. 自动模式

根据数据库可用性自动选择：

```bash
python start_api_server.py
# 选择 "3. 自动选择"
```

## 🔧 环境变量配置

创建 `.env` 文件：

```env
# LLM API密钥
BASIC_LLM_API_KEY=your_basic_llm_api_key
REASONING_LLM_API_KEY=your_reasoning_llm_api_key

# 高德地图API密钥
AMAP_API_KEY=your_amap_api_key

# 可选：覆盖数据库配置
# MONGODB_PASSWORD=your_mongodb_password
```

## 📊 监控和日志

### 健康检查

```bash
curl http://localhost:8000/health
```

返回服务状态和数据库连接信息。

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看API专用日志
tail -f logs/android_api.log
```

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查网络连接
   - 验证数据库配置
   - 确认防火墙设置

2. **API响应慢**
   - 检查网络延迟
   - 优化数据库查询
   - 启用缓存

3. **CORS错误**
   - 检查允许的域名设置
   - 确认请求头配置

### 调试模式

启用调试模式获取详细错误信息：

```bash
# 设置环境变量
export APP_DEBUG=true
python start_api_server.py
```

## 📞 技术支持

如有问题：

1. 查看 `/docs` 端点的API文档
2. 运行 `python test_api.py` 进行诊断
3. 检查日志文件获取错误信息
4. 联系开发团队获取支持

---

**AutoPilot AI** - 为Android应用提供强大的旅行规划后端服务
