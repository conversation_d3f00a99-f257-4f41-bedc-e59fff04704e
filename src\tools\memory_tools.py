"""
记忆系统工具集

提供Agent可使用的记忆相关工具函数。
"""
from typing import Dict, List, Any, Optional
from src.tools.registry import tool_registry
from src.memory.memory_manager import memory_manager
from src.models.travel_models import User, UserProfile
from src.core.logger import get_logger

logger = get_logger('tools.memory')


@tool_registry.register(
    name="get_user_profile",
    description="获取用户画像信息",
    category="memory"
)
async def get_user_profile(user_id: str) -> Dict[str, Any]:
    """
    获取用户画像
    
    Args:
        user_id: 用户ID
        
    Returns:
        用户画像信息
    """
    try:
        user = await memory_manager.get_user_profile(user_id)
        if user:
            return {
                "success": True,
                "user_profile": user.model_dump()
            }
        else:
            return {
                "success": False,
                "message": "User profile not found",
                "user_profile": None
            }
    except Exception as e:
        logger.error(f"Failed to get user profile: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "user_profile": None
        }


@tool_registry.register(
    name="save_user_profile",
    description="保存或更新用户画像",
    category="memory"
)
async def save_user_profile(
    user_id: str,
    name: Optional[str] = None,
    avatar: Optional[str] = None,
    preferred_budget: str = "medium",
    preferred_activities: List[str] = None,
    tags: List[str] = None
) -> Dict[str, Any]:
    """
    保存用户画像
    
    Args:
        user_id: 用户ID
        name: 用户姓名
        avatar: 头像URL
        preferred_budget: 预算偏好
        preferred_activities: 活动偏好
        tags: 用户标签
        
    Returns:
        保存结果
    """
    try:
        # 创建用户画像对象
        profile = UserProfile(
            name=name,
            avatar=avatar,
            preferred_budget=preferred_budget,
            preferred_activities=preferred_activities or [],
            tags=tags or []
        )
        
        user = User(
            user_id=user_id,
            profile=profile
        )
        
        success = await memory_manager.save_user_profile(user)
        
        return {
            "success": success,
            "message": "User profile saved successfully" if success else "Failed to save user profile"
        }
        
    except Exception as e:
        logger.error(f"Failed to save user profile: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@tool_registry.register(
    name="update_user_preferences",
    description="更新用户偏好设置",
    category="memory"
)
async def update_user_preferences(
    user_id: str,
    preferences: Dict[str, Any]
) -> Dict[str, Any]:
    """
    更新用户偏好
    
    Args:
        user_id: 用户ID
        preferences: 偏好设置字典
        
    Returns:
        更新结果
    """
    try:
        success = await memory_manager.update_user_preferences(user_id, preferences)
        
        return {
            "success": success,
            "message": "User preferences updated successfully" if success else "Failed to update preferences"
        }
        
    except Exception as e:
        logger.error(f"Failed to update user preferences: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@tool_registry.register(
    name="get_user_itineraries",
    description="获取用户的历史行程记录",
    category="memory"
)
async def get_user_itineraries(
    user_id: str,
    limit: int = 10
) -> Dict[str, Any]:
    """
    获取用户行程记录
    
    Args:
        user_id: 用户ID
        limit: 返回数量限制
        
    Returns:
        行程记录列表
    """
    try:
        itineraries = await memory_manager.get_user_itineraries(user_id, limit)
        
        return {
            "success": True,
            "itineraries": itineraries,
            "count": len(itineraries)
        }
        
    except Exception as e:
        logger.error(f"Failed to get user itineraries: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "itineraries": []
        }


@tool_registry.register(
    name="get_itinerary_by_trace_id",
    description="根据追踪ID获取特定行程",
    category="memory"
)
async def get_itinerary_by_trace_id(trace_id: str) -> Dict[str, Any]:
    """
    根据trace_id获取行程
    
    Args:
        trace_id: 行程追踪ID
        
    Returns:
        行程详情
    """
    try:
        itinerary = await memory_manager.get_itinerary_by_trace_id(trace_id)
        
        if itinerary:
            return {
                "success": True,
                "itinerary": itinerary
            }
        else:
            return {
                "success": False,
                "message": "Itinerary not found",
                "itinerary": None
            }
            
    except Exception as e:
        logger.error(f"Failed to get itinerary: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "itinerary": None
        }


@tool_registry.register(
    name="save_interaction_log",
    description="保存AI交互日志",
    category="memory"
)
async def save_interaction_log(
    interaction_id: str,
    user_id: str,
    query: str,
    status: str = "PROCESSING",
    metadata: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    保存交互日志
    
    Args:
        interaction_id: 交互ID
        user_id: 用户ID
        query: 用户查询
        status: 状态
        metadata: 元数据
        
    Returns:
        保存结果
    """
    try:
        log_data = {
            "interaction_id": interaction_id,
            "user_id": user_id,
            "query": query,
            "status": status,
            "metadata": metadata or {}
        }
        
        success = await memory_manager.save_interaction_log(log_data)
        
        return {
            "success": success,
            "message": "Interaction log saved successfully" if success else "Failed to save log"
        }
        
    except Exception as e:
        logger.error(f"Failed to save interaction log: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@tool_registry.register(
    name="get_user_statistics",
    description="获取用户统计信息",
    category="memory"
)
async def get_user_statistics(user_id: str) -> Dict[str, Any]:
    """
    获取用户统计信息
    
    Args:
        user_id: 用户ID
        
    Returns:
        统计信息
    """
    try:
        stats = await memory_manager.get_user_statistics(user_id)
        
        return {
            "success": True,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get user statistics: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "statistics": {}
        }


@tool_registry.register(
    name="analyze_user_behavior",
    description="分析用户行为模式，提供个性化建议",
    category="memory"
)
async def analyze_user_behavior(user_id: str) -> Dict[str, Any]:
    """
    分析用户行为模式
    
    Args:
        user_id: 用户ID
        
    Returns:
        行为分析结果和建议
    """
    try:
        # 获取用户画像
        user_profile_result = await get_user_profile(user_id)
        user_profile = user_profile_result.get('user_profile')
        
        # 获取历史行程
        itineraries_result = await get_user_itineraries(user_id, limit=20)
        itineraries = itineraries_result.get('itineraries', [])
        
        # 获取统计信息
        stats_result = await get_user_statistics(user_id)
        stats = stats_result.get('statistics', {})
        
        # 简单的行为分析
        analysis = {
            "travel_frequency": "high" if stats.get('total_itineraries', 0) > 10 else "medium" if stats.get('total_itineraries', 0) > 3 else "low",
            "preferred_destinations": stats.get('popular_destinations', []),
            "activity_level": "active" if stats.get('recent_activity', 0) > 5 else "moderate",
            "recommendations": []
        }
        
        # 生成个性化建议
        if user_profile:
            preferred_activities = user_profile.get('profile', {}).get('preferred_activities', [])
            if '美食' in preferred_activities:
                analysis['recommendations'].append("推荐探索更多美食目的地")
            if '历史古迹' in preferred_activities:
                analysis['recommendations'].append("推荐历史文化深度游")
        
        return {
            "success": True,
            "analysis": analysis
        }
        
    except Exception as e:
        logger.error(f"Failed to analyze user behavior: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "analysis": {}
        }
