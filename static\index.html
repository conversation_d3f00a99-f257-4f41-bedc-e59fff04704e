<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能旅行规划助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-compass"></i> 智能旅行规划助手
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="#" id="historyBtn">
                        <i class="bi bi-clock-history"></i> 历史记录
                    </a>
                </div>
            </div>
        </nav>

        <div class="row">
            <!-- 左侧输入区域 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-chat-dots"></i> 旅行规划</h5>
                    </div>
                    <div class="card-body">
                        <form id="planningForm">
                            <div class="mb-3">
                                <label for="userId" class="form-label">用户ID</label>
                                <input type="text" class="form-control" id="userId" value="user_001" required>
                            </div>
                            <div class="mb-3">
                                <label for="query" class="form-label">旅行需求</label>
                                <textarea class="form-control" id="query" rows="4" 
                                    placeholder="例如：我想去北京玩3天，喜欢历史古迹和美食，预算中等..." required></textarea>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="streamMode" checked>
                                    <label class="form-check-label" for="streamMode">
                                        实时流式规划
                                    </label>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary w-100" id="planBtn">
                                <i class="bi bi-play-circle"></i> 开始规划
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 进度显示 -->
                <div class="card mt-3" id="progressCard" style="display: none;">
                    <div class="card-header">
                        <h6><i class="bi bi-activity"></i> 规划进度</h6>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-2">
                            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="currentStep" class="text-muted">准备开始...</div>
                    </div>
                </div>

                <!-- 思考过程 -->
                <div class="card mt-3" id="thinkingCard" style="display: none;">
                    <div class="card-header">
                        <h6><i class="bi bi-lightbulb"></i> 思考过程</h6>
                    </div>
                    <div class="card-body">
                        <div id="thinkingContent" class="thinking-content"></div>
                    </div>
                </div>
            </div>

            <!-- 右侧结果展示区域 -->
            <div class="col-md-8">
                <!-- 欢迎信息 -->
                <div class="card" id="welcomeCard">
                    <div class="card-body text-center">
                        <i class="bi bi-compass display-1 text-primary"></i>
                        <h3 class="mt-3">欢迎使用智能旅行规划助手</h3>
                        <p class="text-muted">请在左侧输入您的旅行需求，我将为您制定个性化的旅行计划</p>
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="feature-item">
                                    <i class="bi bi-geo-alt-fill text-success"></i>
                                    <h6>智能路线规划</h6>
                                    <small class="text-muted">基于高德地图API的精准路线</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-item">
                                    <i class="bi bi-heart-fill text-danger"></i>
                                    <h6>个性化推荐</h6>
                                    <small class="text-muted">根据您的喜好定制行程</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-item">
                                    <i class="bi bi-clock-fill text-warning"></i>
                                    <h6>实时规划</h6>
                                    <small class="text-muted">流式输出，实时查看规划过程</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 规划结果 -->
                <div class="card" id="resultCard" style="display: none;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-map"></i> 旅行规划结果</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" id="exportBtn">
                                <i class="bi bi-download"></i> 导出
                            </button>
                            <button class="btn btn-sm btn-outline-success" id="shareBtn">
                                <i class="bi bi-share"></i> 分享
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 行程概要 -->
                        <div id="tripSummary" class="mb-4"></div>
                        
                        <!-- 每日行程 -->
                        <div id="dailyPlans"></div>
                        
                        <!-- 高德地图链接 -->
                        <div id="mapLinks" class="mt-4"></div>
                    </div>
                </div>

                <!-- 错误信息 -->
                <div class="alert alert-danger" id="errorAlert" style="display: none;">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span id="errorMessage"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-clock-history"></i> 历史记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="historyContent">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-info-circle"></i> 行程详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="detailContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
