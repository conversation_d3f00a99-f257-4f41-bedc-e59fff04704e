#!/usr/bin/env python3
"""
API测试脚本

快速测试旅行规划API的功能，验证Android调用接口。
"""
import asyncio
import aiohttp
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def test_api_endpoints(base_url="http://localhost:8000"):
    """测试API端点"""
    print(f"🧪 Testing API endpoints at {base_url}")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        
        # 1. 测试根端点
        print("1. Testing root endpoint...")
        try:
            async with session.get(f"{base_url}/") as response:
                data = await response.json()
                print(f"   ✅ Status: {response.status}")
                print(f"   📄 Response: {data}")
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
        
        print()
        
        # 2. 测试健康检查
        print("2. Testing health check...")
        try:
            async with session.get(f"{base_url}/health") as response:
                data = await response.json()
                print(f"   ✅ Status: {response.status}")
                print(f"   🏥 Health: {data}")
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
        
        print()
        
        # 3. 测试旅行规划API
        print("3. Testing travel planning API...")
        test_request = {
            "user_id": "test_android_user",
            "query": "我想去北京玩3天，喜欢历史古迹和美食",
            "context": {
                "platform": "android",
                "version": "1.0.0"
            }
        }
        
        try:
            async with session.post(
                f"{base_url}/travel-planner/plan",
                json=test_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                data = await response.json()
                print(f"   ✅ Status: {response.status}")
                print(f"   🗺️ Planning Result:")
                print(f"      Success: {data.get('success', False)}")
                print(f"      Trace ID: {data.get('trace_id', 'N/A')}")
                if 'data' in data and 'itinerary' in data['data']:
                    itinerary = data['data']['itinerary']
                    print(f"      Title: {itinerary.get('summary', {}).get('title', 'N/A')}")
                    print(f"      Days: {itinerary.get('summary', {}).get('duration_days', 'N/A')}")
                if 'message' in data:
                    print(f"      Message: {data['message']}")
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
        
        print()
        
        # 4. 测试用户历史记录
        print("4. Testing user history API...")
        try:
            async with session.get(f"{base_url}/travel-planner/history/test_android_user") as response:
                data = await response.json()
                print(f"   ✅ Status: {response.status}")
                print(f"   📚 History: {data}")
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
        
        print()
        
        # 5. 测试API文档
        print("5. Testing API documentation...")
        try:
            async with session.get(f"{base_url}/docs") as response:
                print(f"   ✅ Docs Status: {response.status}")
                print(f"   📖 API Docs available at: {base_url}/docs")
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")


def generate_android_example():
    """生成Android调用示例"""
    print("\n📱 Android调用示例")
    print("=" * 50)
    
    # Java/Kotlin示例
    java_example = '''
// Java/Kotlin HTTP请求示例
public class TravelPlannerAPI {
    private static final String BASE_URL = "http://your-server:8000";
    
    public void planTravel(String userId, String query) {
        JSONObject request = new JSONObject();
        try {
            request.put("user_id", userId);
            request.put("query", query);
            request.put("context", new JSONObject().put("platform", "android"));
            
            // 使用OkHttp或Retrofit发送POST请求
            RequestBody body = RequestBody.create(
                request.toString(), 
                MediaType.parse("application/json")
            );
            
            Request httpRequest = new Request.Builder()
                .url(BASE_URL + "/travel-planner/plan")
                .post(body)
                .build();
                
            // 执行请求...
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
'''
    
    # cURL示例
    curl_example = '''
# cURL命令示例
curl -X POST "http://localhost:8000/travel-planner/plan" \\
  -H "Content-Type: application/json" \\
  -d '{
    "user_id": "android_user_001",
    "query": "我想去上海玩2天，喜欢现代建筑和购物",
    "context": {
      "platform": "android",
      "version": "1.0.0"
    }
  }'
'''
    
    print("Java/Kotlin示例:")
    print(java_example)
    print("\ncURL示例:")
    print(curl_example)


def print_api_info():
    """打印API信息"""
    print("\n📡 API接口信息")
    print("=" * 50)
    
    endpoints = [
        ("GET", "/", "根端点，返回API基本信息"),
        ("GET", "/health", "健康检查，返回服务状态"),
        ("POST", "/travel-planner/plan", "创建旅行规划"),
        ("GET", "/travel-planner/history/{user_id}", "获取用户历史记录"),
        ("GET", "/docs", "API文档 (Swagger UI)"),
        ("GET", "/redoc", "API文档 (ReDoc)")
    ]
    
    for method, path, description in endpoints:
        print(f"  {method:6} {path:35} - {description}")
    
    print(f"\n🌐 默认服务地址: http://localhost:8000")
    print(f"📖 API文档地址: http://localhost:8000/docs")


async def main():
    """主函数"""
    print("🚀 AutoPilot AI - API测试工具")
    print("专为Android应用后端API设计")
    print("=" * 50)
    
    # 检查服务器是否运行
    print("检查API服务器状态...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/health", timeout=5) as response:
                if response.status == 200:
                    print("✅ API服务器正在运行")
                    
                    # 运行测试
                    await test_api_endpoints()
                    
                    # 显示Android示例
                    generate_android_example()
                    
                    # 显示API信息
                    print_api_info()
                    
                else:
                    print(f"⚠️ API服务器响应异常: {response.status}")
    
    except Exception as e:
        print(f"❌ 无法连接到API服务器: {str(e)}")
        print("\n💡 请先启动API服务器:")
        print("   python start_api_server.py")
        print("   或")
        print("   python run.py server")
        
        # 仍然显示API信息
        print_api_info()


if __name__ == "__main__":
    asyncio.run(main())
