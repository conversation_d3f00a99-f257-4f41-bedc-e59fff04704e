# AutoPilot AI - 智能旅行规划助手依赖

# Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# 数据库
motor>=3.3.0  # MongoDB异步驱动
pymongo>=4.6.0

# 数据模型和验证
pydantic>=2.5.0
pydantic-settings>=2.1.0

# HTTP客户端
aiohttp>=3.9.0
httpx>=0.25.0
requests>=2.32.3

# 配置和环境
python-dotenv>=1.0.0
PyYAML>=6.0.1

# 日志
structlog>=23.2.0

# 测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# 开发工具
black>=23.0.0
mypy>=1.7.0
flake8>=6.1.0

# LLM相关
openai>=1.3.0  # OpenAI兼容接口

# 其他工具
python-multipart>=0.0.6  # FastAPI文件上传支持
jinja2>=3.1.0  # 模板引擎

# 原有依赖（保留兼容性）
dataclasses-json==0.6.4
pycryptodome~=3.22.0
crypto~=1.4.1