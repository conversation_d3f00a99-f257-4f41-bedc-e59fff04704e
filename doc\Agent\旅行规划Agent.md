# 旅行规划Agent - 工作流程与设计文档

本文档旨在定义一个集成了高德地图（Amap）MCP工具集、具备用户记忆和个性化能力的智能旅行规划Agent。Agent的目标是理解用户的复杂、口语化的旅行需求，通过调用外部工具获取实时信息，并结合用户偏好，最终生成结构化、可执行的旅行计划。

---

### 1. Agent 定位与目标

- **定位**: 一个懂旅行、懂用户的智能规划专家。
- **核心目标**:
    1.  **意图精准理解**: 准确解析用户输入，识别出发点、目的地、时间、预算、成员、兴趣点（如"亲子"、"网红打卡地"）等信息。
    2.  **个性化推荐**: 结合用户的历史行为、画像标签（存储于MongoDB中），提供千人千面的规划结果。
    3.  **动态信息整合**: 实时调用高德地图API获取天气、路况、POI信息，确保计划的时效性和准确性。
    4.  **结构化输出**: 生成标准化的JSON格式行程单，与前端（Web/小程序）解耦，易于渲染和二次开发。
    5.  **持续学习**: 将每次成功的规划和用户反馈存储为记忆，不断优化未来的推荐逻辑。

---

### 2. 核心能力：工具集（Amap MCP Tools）

Agent的能力来自于对以下15个高德API工具的编排和调用。

| 分类 | 工具名称 | 核心功能与用途 |
| :--- | :--- | :--- |
| **基础定位与地址解析** | `maps_geo` | **地址 → 经纬度**：所有空间计算的基础，流程的起点。 |
| | `maps_regeocode` | **经纬度 → 地址**：用于丰富地点信息，提供可读地址。 |
| | `maps_ip_location` | **IP地址 → 城市**：当用户未明确指定城市时，用于猜测默认城市。 |
| **路线规划与距离计算** | `maps_direction_driving` | **驾车路线规划**：用于规划城际、市内自驾路线。 |
| | `maps_direction_walking` | **步行路线规划**：用于景点之间、停车场到景点的短途路线。 |
| | `maps_direction_bicycling` | **骑行路线规划**：适用于城市慢行、景区游览。 |
| | `maps_direction_transit_integrated` | **公共交通路线规划**：适用于公共交通出行的用户。 |
| | `maps_distance` | **距离测量**：轻量级计算，用于快速筛选POI。 |
| **信息检索与周边搜索** | `maps_text_search` | **关键字搜索(POI)**：根据用户提到的特定名称搜索地点。 |
| | `maps_around_search` | **周边搜索(POI)**：**Agent核心工具**，用于动态发现目的的周边的美食、景点、停车场等。|
| | `maps_search_detail` | **POI 详情查询**：获取特定POI的评分、图片、营业时间等丰富信息。|
| **辅助上下文信息** | `maps_weather` | **天气查询**：决策行程安排的关键依据（如雨天推荐室内活动）。 |
| **高德App集成与唤醒** | `maps_schema_personal_map` | **生成行程地图链接**：将完整行程打包成一个高德地图链接。 |
| | `maps_schema_navi` | **生成导航链接**：为单段路程提供"一键导航"功能。 |
| | `maps_schema_take_taxi` | **生成打车链接**：提供便捷的出行方式。 |

---

### 3. 核心工作流（The Core Workflow）

Agent的工作流被设计为一个四阶段的、集成了数据库交互的闭环流程。

```mermaid
flowchart TD
    A["用户输入<br/>周末带孩子从亦庄开车去故宫"] 
    
    A --> B["Phase 1: 意图理解与个性化融合"]
    B --> B_DB[("读取用户数据库<br/>MongoDB")]
    B_DB --> B1["构建个性化查询指令"]
    
    B1 --> C["Phase 2: 动态工具规划与并行执行"]
    
    C --> C_PHASE1["第一层并行调用"]
    C_PHASE1 --> C1["geo - 起点地址解析"]
    C_PHASE1 --> C2["geo - 目的地地址解析"]  
    C_PHASE1 --> C3["weather - 天气查询"]
    
    C1 --> WAIT1["等待地理坐标返回"]
    C2 --> WAIT1
    C3 --> WAIT1
    
    WAIT1 --> C_PHASE2["第二层并行调用<br/>依赖地理位置"]
    
    C_PHASE2 --> D1["direction_driving<br/>驾车路线规划"]
    C_PHASE2 --> D2["around_search<br/>停车场搜索"]
    C_PHASE2 --> D3["around_search<br/>亲子美食搜索"]
    C_PHASE2 --> D4["around_search<br/>亲子景点搜索"]
    
    D1 --> WAIT2["等待所有API返回"]
    D2 --> WAIT2
    D3 --> WAIT2
    D4 --> WAIT2
    
    WAIT2 --> F["Phase 3: 数据综合与智能决策"]
    F --> F1["天气影响分析"]
    F --> F2["POI筛选与评分"]
    F --> F3["行程路线编排"]
    
    F1 --> G["Phase 4: 结构化结果生成与记忆存储"]
    F2 --> G
    F3 --> G
    
    G --> G1["生成结构化JSON"]
    G --> G2["生成高德地图链接"]
    G --> G3["写入记忆数据库"]
    
    G3 --> G_DB[("写入行程与记忆<br/>MongoDB")]
    
    G1 --> H["输出: 结构化JSON行程单"]
    G2 --> H
    G_DB --> H
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style F fill:#fff3e0
    style G fill:#fce4ec
    style H fill:#e0f2f1
```

#### **Phase 1: 意图理解与个性化融合 (Intent Parsing & Personalization)**

-   **目标**: 将模糊的自然语言转化为结构化的、带有用户偏好的查询指令。
-   **步骤**:
    1.  **实体抽取**: 从用户输入中提取核心实体：
        -   `出发点`: 亦庄
        -   `目的地`: 故宫
        -   `出行方式`: 开车
        -   `时间`: 周末 (解析为具体日期)
        -   `修饰词/偏好`: "带孩子" -> `亲子`, "好停车" -> `停车场`, "小孩吃的餐厅" -> `美食`
    2.  **用户画像加载**: 根据`UserID`，从MongoDB的`users`和`memories`集合中读取用户数据。
        -   `历史偏好`: 之前是否去过类似景点？是否偏爱自然风光或历史古迹？
        -   `消费习惯`: 预算是经济型还是豪华型？
        -   `标签`: `旅游搭子`信息，家庭成员（有儿童）等。
    3.  **查询指令构建**: 将提取的实体和用户画像融合，形成内部查询对象。
        ```json
        {
          "origin": "北京亦庄",
          "destination": "北京故宫",
          "transportMode": "driving",
          "dates": ["2025-06-21", "2025-06-22"],
          "userProfile": { "has_children": true, "budget": "medium" },
          "interests": [
            { "type": "parking", "priority": 1 },
            { "type": "food", "tags": ["亲子餐厅", "儿童餐"], "priority": 2 },
            { "type": "attraction", "tags": ["适合儿童", "室内"], "priority": 3 }
          ]
        }
        ```

#### **Phase 2: 动态工具规划与并行执行 (Dynamic Tool Planning & Execution)**

-   **目标**: 基于内部查询指令，智能规划需要调用的工具组合，并以最高效的方式（并行）执行。
-   **策略**:
    1.  **第一层并行 (基础信息)**: 并发调用无依赖的工具。
        -   `maps_geo(address="北京亦庄")`
        -   `maps_geo(address="故宫")`
        -   `maps_weather(city="北京")`
    2.  **第二层并行 (核心信息)**: 待第一层`maps_geo`返回目的地坐标`dest_loc`后，并发调用所有依赖该坐标的工具。
        -   `maps_direction_driving(origin_loc, dest_loc)`
        -   `maps_around_search(location=dest_loc, keywords="停车场")`
        -   `maps_around_search(location=dest_loc, keywords="亲子餐厅")` (根据用户偏好动态生成关键词)
        -   `maps_around_search(location=dest_loc, keywords="科技馆 OR 儿童乐园")` (同上)

#### **Phase 3: 数据综合与智能决策 (Data Synthesis & Intelligent Reasoning)**

-   **目标**: Agent的"大脑"。将API返回的零散数据，结合用户偏好和现实规则，整合成一个逻辑自洽、高质量的旅行计划。
-   **决策逻辑示例**:
    -   **天气影响**: `weather_info`显示周六有雨，则将室外景点（如天安门广场）的优先级调低，提升室内景点（如中国科学技术馆）的推荐权重。
    -   **POI筛选**:
        -   `停车场`: 过滤掉距离目的地步行超过15分钟的，并按价格从低到高排序。
        -   `餐厅`: 优先选择`photos`字段不为空、`rating` > 4.0，且`tag`包含"亲子"的餐厅。
    -   **行程编排**:
        -   根据景点的`estimatedDuration`（预估游玩时间）和地理位置，使用简单的路径规划算法（如贪心算法）安排每日行程，确保路线不绕路，时间不紧张。
        -   将筛选出的餐厅和停车场就近匹配到每日的景点中。

#### **Phase 4: 结构化结果生成与记忆存储 (Structured Response Generation & Memory Storage)**

-   **目标**: 输出标准化的数据，并为未来的学习沉淀数据资产。
-   **步骤**:
    1.  **生成结构化JSON**: 将整合后的行程数据，构造成前端易于渲染的、定义清晰的JSON对象（参考`cursor_.md`中的示例JSON）。
    2.  **生成便捷链接**:
        -   调用 `maps_schema_personal_map`，将整个行程生成一个高德地图行程链接。
        -   为关键节点（如"酒店->景点"、"景点->餐厅"）生成`maps_schema_navi`导航链接。
        -   将这些链接嵌入到最终的JSON中。
    3.  **写入记忆数据库**: 将本次规划的关键信息存入MongoDB。
        -   **`trips`集合**: 存储完整的行程单JSON。
        -   **`memories`集合**: 形成新的记忆条目。例如：`{userId: "xxx", event: "规划了故宫亲子游", feedback: "positive", generated_tags: ["故宫", "亲子", "自驾"]}`。这为未来的个性化推荐提供了宝贵的数据。

---

### 4. 调用策略示例

-   **简单查询**: "查一下北京今天的天气" -> `maps_weather` -> 直接回答。
-   **中等查询**: "从三里屯开车到颐和园要多久？" -> 并行`maps_geo` -> `maps_direction_driving` -> 格式化回答。
-   **复杂查询**: (如本文档开篇示例) -> 完整执行上述四个阶段的完整工作流。
