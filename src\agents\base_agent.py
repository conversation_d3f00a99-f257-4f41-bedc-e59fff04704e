"""
基础Agent类

提供所有Agent的通用功能和接口定义。
"""
import uuid
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime
from pydantic import BaseModel, Field
from src.core.logger import get_logger
from src.tools.registry import tool_registry
from src.tools.sse_output import sse_manager, SSEEventType

logger = get_logger('agents.base')


class AgentState(BaseModel):
    """Agent状态模型"""
    trace_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str
    session_id: Optional[str] = None
    status: str = Field(default="initialized")  # initialized, thinking, executing, completed, failed
    current_step: str = Field(default="")
    progress: float = Field(default=0.0)
    messages: List[Dict[str, Any]] = Field(default_factory=list)
    context: Dict[str, Any] = Field(default_factory=dict)
    tools_used: List[str] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class BaseAgent(ABC):
    """基础Agent抽象类"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.logger = get_logger(f'agents.{name.lower()}')
        self._state: Optional[AgentState] = None
    
    @property
    def state(self) -> Optional[AgentState]:
        """获取当前状态"""
        return self._state
    
    def initialize_state(self, user_id: str, session_id: Optional[str] = None) -> AgentState:
        """初始化Agent状态"""
        self._state = AgentState(
            user_id=user_id,
            session_id=session_id
        )
        self.logger.info(f"Initialized agent state: {self._state.trace_id}")
        return self._state
    
    async def update_status(self, status: str, step: str = "", progress: float = None):
        """更新状态"""
        if self._state:
            self._state.status = status
            if step:
                self._state.current_step = step
            if progress is not None:
                self._state.progress = progress
            self._state.updated_at = datetime.now()
            
            # 发送SSE更新
            if self._state.session_id:
                await sse_manager.send_progress(
                    self._state.session_id,
                    step or status,
                    progress or self._state.progress,
                    self._state.trace_id
                )
    
    async def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None):
        """添加消息到状态"""
        if self._state:
            message = {
                "role": role,
                "content": content,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            self._state.messages.append(message)
            
            # 发送SSE消息
            if self._state.session_id and role == "assistant":
                await sse_manager.send_thinking(
                    self._state.session_id,
                    content,
                    self._state.trace_id
                )
    
    async def use_tool(self, tool_name: str, **kwargs) -> Any:
        """使用工具"""
        if not self._state:
            raise ValueError("Agent state not initialized")
        
        self.logger.info(f"Using tool: {tool_name}")
        
        # 记录工具使用
        if tool_name not in self._state.tools_used:
            self._state.tools_used.append(tool_name)
        
        # 发送工具调用SSE消息
        if self._state.session_id:
            await sse_manager.send_tool_call(
                self._state.session_id,
                tool_name,
                kwargs,
                self._state.trace_id
            )
        
        try:
            result = await tool_registry.execute_tool(tool_name, **kwargs)
            
            # 发送工具结果SSE消息
            if self._state.session_id:
                await sse_manager.send_tool_result(
                    self._state.session_id,
                    tool_name,
                    result,
                    self._state.trace_id
                )
            
            return result
        
        except Exception as e:
            self.logger.error(f"Tool {tool_name} execution failed: {str(e)}")
            
            # 发送错误SSE消息
            if self._state.session_id:
                await sse_manager.send_error(
                    self._state.session_id,
                    f"Tool {tool_name} failed: {str(e)}",
                    self._state.trace_id
                )
            
            raise
    
    async def get_available_tools(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        if category:
            tools = tool_registry.get_tools_by_category(category)
            return [tool_registry.get_tool_info(name) for name in tools.keys()]
        else:
            return tool_registry.get_all_tools_info()
    
    @abstractmethod
    async def process(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理用户查询的抽象方法
        
        Args:
            query: 用户查询
            context: 上下文信息
            
        Returns:
            处理结果
        """
        pass
    
    @abstractmethod
    async def stream_process(self, query: str, context: Dict[str, Any] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """
        流式处理用户查询的抽象方法
        
        Args:
            query: 用户查询
            context: 上下文信息
            
        Yields:
            处理过程中的中间结果
        """
        pass
    
    async def cleanup(self):
        """清理资源"""
        if self._state and self._state.session_id:
            # 发送完成消息
            await sse_manager.send_complete(
                self._state.session_id,
                {"status": "cleanup_completed"},
                self._state.trace_id
            )
        
        self.logger.info(f"Agent {self.name} cleanup completed")
