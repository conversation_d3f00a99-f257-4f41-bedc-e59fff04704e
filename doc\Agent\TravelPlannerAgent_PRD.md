# 智能旅行规划Agent - 产品需求文档 (PRD)

| 版本 | 日期       | 作者   | 状态 | 变更描述               |
| :--- | :--------- | :----- | :--- | :--------------------- |
| 1.0  | 2024-07-30 | Gemini | 草稿 | 初始版本，整合所有技术细节 |

---

## 1. 文档概述 (Overview)

### 1.1 文档目的 (Purpose)
本文档旨在明确定义 **智能旅行规划Agent (TravelPlannerAgent)** 的产品需求、功能规格、技术架构和验收标准。它将作为研发、测试、产品团队之间沟通协作的基准，确保各方对产品有统一、清晰的理解。

### 1.2 项目背景 (Background)
当前用户进行旅行规划时，往往需要在多个应用（地图、点评、预订网站）之间切换，过程繁琐且信息碎片化。本项目旨在打造一个**对话式AI旅行规划入口**，通过一个统一的智能Agent，理解用户的口语化、模糊化需求，自动完成信息检索、路线规划、智能决策等一系列复杂任务，提供一站式的、个性化的旅行规划服务。

### 1.3 核心目标 (Core Objectives)
1.  **提升规划效率**: 将传统数小时的规划工作缩短至分钟级。
2.  **增强个性化体验**: 结合用户历史与偏好，提供"比用户更懂自己"的旅行建议。
3.  **保证信息时效性**: 实时调用外部API，确保路线、天气、POI信息的准确性。
4.  **优化交互体验**: 通过实时流式反馈，让用户清晰感知Agent的"思考过程"，降低等待焦虑。

---

## 2. 功能需求 (Functional Requirements)

### FR-01: 自然语言旅行意图理解
-   **描述**: Agent必须能精准解析用户的自然语言输入，提取所有与旅行规划相关的核心实体和隐含意图。
-   **输入示例**:
    -   `"周末带孩子从北京亦庄开车去故宫，找个好停车的地方和适合小孩吃的餐厅"`
    -   `"下周去上海出差，帮我推荐一个离静安寺近、带健身房的五星级酒店"`
    -   `"明天天气怎么样？适合去户外爬山吗？"`
-   **必须提取的实体**: 出发点、目的地、时间、出行方式、成员构成（亲子/情侣/单独）、预算、兴趣标签（网红/小众/美食）、特定需求（停车/充电/无障碍）。
-   **技术实现**: 使用`reasoning_llm`（如 `glm-z1-flash`）进行复杂的意图分析和实体抽取。

### FR-02: 个性化推荐与记忆
-   **描述**: Agent的推荐必须结合用户的历史行为、画像标签和过往行程记录。
-   **实现逻辑**:
    1.  接收到请求后，根据`user_id`从MongoDB的`users`集合中加载用户画像（如`{ "tags": ["美食爱好者", "亲子游"], "preferred_budget": "medium" }`）。
    2.  将用户画像作为关键上下文，影响后续的工具调用参数（如搜索POI时，自动加上"亲子"关键词）和决策逻辑（如预算筛选）。
    3.  任务结束后，将本次行程的关键信息（如目的地、标签）更新到用户画像和`memories`集合中，形成学习闭环。

### FR-03: 动态工具规划与并行执行
-   **描述**: Agent必须能根据解析出的意图，智能地规划需要调用哪些高德地图工具，并以最高效的方式（并行）执行。
-   **实现逻辑**:
    1.  **工具规划**: 使用`reasoning_llm`，基于意图生成一个工具调用计划（Tool Call Plan），该计划应包含工具名称和参数。
    2.  **并行执行**: 采用分层并行策略。
        -   **第一层 (无依赖)**: 并发调用`maps_geo`（获取起点/终点坐标）、`maps_weather`等基础信息工具。
        -   **第二层 (有依赖)**: 待第一层返回坐标后，并发调用`maps_direction_driving`、`maps_around_search`等核心信息工具。
    3.  **技术栈**: 使用`aiohttp`或`httpx`库实现真正的异步HTTP请求，配合`asyncio.gather`执行并行调用。

### FR-04: 实时思考过程流式输出 (SSE)
-   **描述**: 为了提升用户体验，Agent的完整思考和执行过程，必须以事件流的形式通过Server-Sent Events (SSE)实时推送给客户端。
-   **事件类型**:
    -   `thought`: Agent的思考过程，如"正在分析用户需求..."、"天气有雨，调整计划..."。
    -   `tool_call`: 准备调用某个工具的事件，包含工具名称和参数。
    -   `tool_result`: 工具执行完成的事件，包含工具名称和返回结果的摘要。
    -   `final_result`: 最终行程规划完成的事件。
    -   `error`: 流程中发生错误的事件。
-   **技术实现**: Agent主方法改造为`async def`生成器，使用`yield`推送事件。FastAPI后端使用`StreamingResponse`实现SSE接口。

### FR-05: 结构化行程输出
-   **描述**: Agent最终生成的旅行计划必须是结构化的、定义清晰的JSON格式，便于前端渲染。
-   **技术实现**: 使用`basic_llm`（如`glm-4-flash`）执行格式化任务。接收`reasoning_llm`生成的内部逻辑计划，然后根据预设的Prompt模板，将其转换为最终的用户友好JSON。

### FR-06: 完整执行日志持久化
-   **描述**: 每次规划任务的所有流式事件，必须在任务结束后完整地、按顺序地存入数据库，用于问题排查、数据分析和模型微调。
-   **实现逻辑**:
    1.  Agent在内存中维护一个`List[StreamEvent]`，每次`yield`事件时，也将其追加到此列表。
    2.  在`plan_trip`生成器的`finally`块中，调用数据库客户端，将完整的事件列表一次性写入MongoDB的`trip_execution_logs`集合。

---

## 3. 系统设计与技术架构

### 3.1 架构模式: "形单神多"的单Agent架构
-   **实现上是单体**: 所有业务逻辑封装在`TravelPlannerAgent`一个类中，通过内部方法调用进行协作。便于初期快速开发和调试。
-   **思想上是多体**: `TravelPlannerAgent`内部通过不同的方法，扮演了规划器、意图分析器、工具执行器、决策器等多个角色，实现了高度的职责分离。为未来平滑演进到真正的多Agent系统（如AutoGen）奠定了基础。

### 3.2 (新增) 核心架构: 工作流即图 (Workflow as a Graph)
**背景**: 受到`deer-flow-main`等业界领先项目的启发，为增强系统的健壮性、可维护性和可观测性，`TravelPlannerAgent`的核心逻辑应采用"工作流即图"的模式进行构建，推荐使用**LangGraph**框架。

**核心思想**:
Agent的执行流程不再是一系列线性的方法调用，而是一个**有向状态图 (Directed Acyclic Graph - DAG)**。
1.  **节点 (Nodes)**: 图中的每个节点代表一个原子操作或一个"专家角色"。例如：
    -   `intent_parser_node`: 解析用户意图。
    -   `tool_planning_node`: 规划需要调用的工具。
    -   `parallel_tool_executor_node`: 并行执行工具调用。
    -   `decision_synthesis_node`: 综合信息进行决策。
    -   `response_formatter_node`: 格式化最终输出。
2.  **边 (Edges)**: 图中的边代表了节点之间的流转关系，可以附带条件。例如，`tool_planning_node`完成后，可以根据"是否需要调用工具"这一条件，决定下一跳是`parallel_tool_executor_node`还是直接到`response_formatter_node`。
3.  **状态 (State)**: 一个统一的状态对象在图中传递。它包含所有需要的数据，如原始查询、解析后的意图、工具调用结果等。每个节点接收当前状态，执行操作，然后更新状态并返回。

**架构图示例**:
```mermaid
graph TD
    A[Start] --> B(intent_parser_node);
    B --> C(tool_planning_node);
    C --> D{has_tools_to_call?};
    D -- Yes --> E(parallel_tool_executor_node);
    E --> F(decision_synthesis_node);
    D -- No --> F;
    F --> G(response_formatter_node);
    G --> H[End];

    style B fill:#e1f5fe
    style C fill:#e8f5e8
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style G fill:#fce4ec
```

**优点**:
-   **可观测性 (Observability)**: 整个执行流程一目了然，便于追踪和调试。
-   **模块化 (Modularity)**: 节点之间高度解耦，可以独立开发、测试和替换。
-   **灵活性 (Flexibility)**: 可以通过修改图的结构，轻松地增删或重排步骤，而无需大规模重构代码。
-   **健壮性 (Robustness)**: 框架（如LangGraph）内置了重试、回退等机制，能方便地处理循环和错误。

### 3.3 数据流: 双通道策略
![双通道数据流](https://i.imgur.com/your-diagram-image.png) (*此处应有新绘制的、结合了SSE和DB的数据流图*)
```mermaid
sequenceDiagram
    participant Client
    participant FastAPI as "API Endpoint"
    participant Agent as "TravelPlannerAgent"
    participant DB as "MongoDB"

    Client->>+FastAPI: POST /api/v1/planner/stream
    FastAPI->>+Agent: plan_trip(query)

    loop 实时流式通道 (SSE)
        Agent->>Agent: 生成事件 (thought, tool_call, ...)
        Note right of Agent: 缓存事件到内部列表
        Agent-->>FastAPI: yield StreamEvent
        FastAPI-->>Client: data: {event...}\n\n
    end

    Agent->>Agent: 任务完成
    Note right of Agent: 最终结果已生成

    Note over Agent, DB: 异步持久化通道
    Agent->>+DB: save_full_log([所有事件])
    DB-->>-Agent: 确认写入

    Agent-->>-FastAPI: 结束生成器
    FastAPI-->>Client: 关闭SSE连接
```

### 3.4 模型策略: 分层模型调用
-   **决策层 (Thinking Layer)**: 使用`reasoning_llm` (`glm-z1-flash`)。负责意图理解、工具规划、数据综合决策等所有复杂的思考任务。输出一个逻辑完备的"内部计划"。
-   **执行层 (Formatting Layer)**: 使用`basic_llm` (`glm-4-flash`)。负责将"内部计划"转换为用户友好的、严格格式化的最终JSON。成本低、速度快。

---

## 4. 外部工具集成: 高德地图 (Amap MCP)

### 4.1 工具列表与能力
Agent必须具备调用以下高德地图API的能力，这些API通过高德MCP SSE接口提供：
-   **定位与地址解析**: `maps_geo`, `maps_regeocode`, `maps_ip_location`
-   **路线规划**: `maps_direction_driving`, `maps_direction_walking`, `maps_direction_bicycling`, `maps_direction_transit_integrated`
-   **信息检索**: `maps_text_search`, `maps_around_search`, `maps_search_detail`
-   **辅助信息**: `maps_weather`, `maps_distance`
-   **App唤醒**: `maps_schema_personal_map`, `maps_schema_navi`, `maps_schema_take_taxi`

### 4.2 配置与认证
-   **接口地址**: `https://mcp.amap.com/sse`
-   **API Key**: `e8f742bdb09f99c8c6b035b7f1f04e66` (此密钥应通过环境变量`AMAP_MCP_API_KEY`加载，而非硬编码)

---

## 5. 数据库设计 (MongoDB)

### 5.1 数据库配置
配置信息从`config/default.yaml`中读取`mongodb`部分：
-   **Host**: `***********`
-   **Port**: `27017`
-   **Username/Password**: 通过环境变量加载
-   **Database**: `dh_platform_data`

### 5.2 数据模型/集合 (Schemas)

#### 1. `users` 集合
存储用户信息和长期画像。
```json
{
  "_id": "ObjectId('user123')",
  "user_id": "user123",
  "created_at": "ISODate()",
  "updated_at": "ISODate()",
  "profile": {
    "name": "张三",
    "avatar": "url"
  },
  "preferences": {
    "preferred_budget": "medium", // low, medium, high
    "preferred_activities": ["美食", "历史古迹"]
  },
  "tags": ["亲子游", "自驾爱好者", "对价格敏感"]
}
```

#### 2. `trips` 集合
存储最终生成的、交付给用户的行程单。
```json
{
  "_id": "ObjectId('trip456')",
  "trace_id": "uuid-for-the-planning-session",
  "user_id": "user123",
  "created_at": "ISODate()",
  "summary": {
    "title": "北京周末亲子文化游",
    "suggestion": "由于周末有雨，已为您优先安排室内活动。"
  },
  "itinerary": [
    {
      "day": 1,
      "theme": "历史与艺术",
      "attractions": [
        { "name": "中国国家博物馆", "estimated_duration": "4小时" }
      ],
      // ... 其他字段
    }
  ],
  "raw_user_query": "周末带孩子去故宫..."
}
```

#### 3. `trip_execution_logs` 集合
存储单次规划任务的完整执行日志，即所有SSE事件的集合。
```json
{
  "_id": "ObjectId('log789')",
  "trace_id": "uuid-for-the-planning-session",
  "user_id": "user123",
  "created_at": "ISODate()",
  "log": [
    {
      "event_id": "uuid-1",
      "event_type": "thought",
      "payload": { "message": "开始解析用户需求..." },
      "timestamp": "ISODate()"
    },
    {
      "event_id": "uuid-2",
      "event_type": "tool_call",
      "payload": { "name": "maps_weather", "params": {"city": "北京"} },
      "timestamp": "ISODate()"
    }
    // ... 更多事件
  ]
}
```

---

## 6. API接口规范

### 6.1 流式规划接口
-   **Endpoint**: `POST /api/v1/planner/stream`
-   **Request Body**:
    ```json
    {
      "user_id": "user123",
      "query": "周末带孩子去故宫玩，车停哪儿方便？",
      "session_id": "optional-session-uuid"
    }
    ```
-   **Response**:
    -   **Content-Type**: `text/event-stream`
    -   **Format**: 每个事件都是`data: <json_string>\n\n`的形式。

### 6.2 SSE事件模型 `StreamEvent`
```json
// The JSON string in the 'data' field of SSE
{
  "event_id": "uuid-of-this-event",
  "trace_id": "uuid-of-the-entire-trip-request",
  "event_type": "thought" | "tool_call" | "tool_result" | "final_result" | "error",
  "payload": {
    // 结构取决于 event_type
  },
  "timestamp": "ISO 8601 string"
}
```

---

## 7. 配置管理

所有配置项应在`config/default.yaml`中定义，并支持通过环境变量覆盖。

### 7.1 `default.yaml` 配置引用
-   `reasoning_llm`: 用于决策层。
-   `basic_llm`: 用于执行层。
-   `mongodb`: 用于数据库连接。
-   `logging`: 用于配置应用日志。

### 7.2 新增配置项
建议在`default.yaml`中增加`amap`配置节：
```yaml
# Amap MCP Tool Settings
amap:
  base_url: "https://mcp.amap.com/sse"
  # api_key 通过环境变量 AMAP_API_KEY 加载
```

---

## 8. 开发里程碑 (Development Milestones)

-   **M1: 核心Agent实现 (Sprint 1)**
    -   [ ] 实现`TravelPlannerAgent`基本类结构。
    -   [ ] 集成`reasoning_llm`完成意图理解。
    -   [ ] 集成高德地图工具，实现**顺序**调用。
    -   [ ] 完成基本的数据综合与决策逻辑。
    -   [ ] **验收标准**: Agent能对简单查询（如"搜索故宫附近的停车场"）返回正确的、结构化的JSON结果。

-   **M2: 流式输出与并行优化 (Sprint 2)**
    -   [ ] 将Agent主方法改造为异步生成器。
    -   [ ] 实现FastAPI的SSE流式接口。
    -   [ ] 将工具调用改造为**并行**模式。
    -   [ ] **验收标准**: 客户端能通过EventSource接收到`thought`, `tool_call`, `final_result`等事件。

-   **M3: 数据库集成与持久化 (Sprint 3)**
    -   [ ] 实现MongoDB的连接与数据模型。
    -   [ ] 完成`trip_execution_logs`的写入逻辑。
    -   [ ] 完成`trips`（最终行程）的写入逻辑。
    -   [ ] **验收标准**: 每次规划后，可以在MongoDB中查到对应的完整日志和最终行程。

-   **M4: 个性化与模型优化 (Sprint 4)**
    -   [ ] 实现`users`用户画像的读取与更新逻辑。
    -   [ ] 将用户偏好融入到Agent的决策流程中。
    -   [ ] 实现`basic_llm`分层调用，优化格式化输出。
    -   [ ] **验收标准**: 对于有历史记录的用户，Agent的推荐能明显体现其偏好。 