"""
测试配置文件

提供测试所需的fixtures和配置。
"""
import pytest
import asyncio
from typing import AsyncGenerator
from httpx import AsyncClient
from src.main import app
from src.core.database import db_manager
from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_models import User, UserProfile


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def setup_database():
    """设置测试数据库"""
    await db_manager.initialize()
    yield
    await db_manager.cleanup()


@pytest.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """异步HTTP客户端"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def sample_user() -> User:
    """示例用户数据"""
    profile = UserProfile(
        name="测试用户",
        preferred_budget="medium",
        preferred_activities=["美食", "历史古迹"],
        tags=["亲子游", "自驾爱好者"]
    )
    
    return User(
        user_id="test_user_001",
        profile=profile
    )


@pytest.fixture
def travel_agent() -> TravelPlannerAgent:
    """旅行规划Agent实例"""
    agent = TravelPlannerAgent()
    agent.initialize_state(user_id="test_user_001")
    return agent


@pytest.fixture
def sample_travel_query() -> str:
    """示例旅行查询"""
    return "我想去北京玩3天，喜欢历史古迹和美食，预算中等"


@pytest.fixture
def sample_context() -> dict:
    """示例上下文数据"""
    return {
        "user_preferences": {
            "budget": "medium",
            "activities": ["历史古迹", "美食"],
            "transport": "public"
        },
        "travel_dates": {
            "start_date": "2024-07-01",
            "duration": 3
        }
    }


@pytest.fixture
async def mock_amap_response():
    """模拟高德地图API响应"""
    return {
        "status": "1",
        "info": "OK",
        "infocode": "10000",
        "pois": [
            {
                "id": "B000A83M6N",
                "name": "故宫博物院",
                "type": "风景名胜;风景名胜相关;风景名胜",
                "typecode": "110000",
                "address": "北京市东城区景山前街4号",
                "location": "116.397026,39.918058",
                "tel": "010-85007421",
                "distance": "",
                "biz_type": "",
                "rating": "4.6",
                "cost": ""
            },
            {
                "id": "B000A7BD6C",
                "name": "天安门广场",
                "type": "风景名胜;风景名胜相关;广场",
                "typecode": "110000",
                "address": "北京市东城区东长安街",
                "location": "116.397477,39.903738",
                "tel": "",
                "distance": "",
                "biz_type": "",
                "rating": "4.5",
                "cost": ""
            }
        ]
    }


@pytest.fixture
def mock_weather_response():
    """模拟天气API响应"""
    return {
        "status": "1",
        "count": "1",
        "info": "OK",
        "infocode": "10000",
        "lives": [
            {
                "province": "北京",
                "city": "北京市",
                "adcode": "110000",
                "weather": "晴",
                "temperature": "25",
                "winddirection": "南",
                "windpower": "≤3",
                "humidity": "45",
                "reporttime": "2024-06-18 15:00:00"
            }
        ]
    }


@pytest.fixture
def expected_itinerary_structure():
    """期望的行程结构"""
    return {
        "trace_id": str,
        "user_id": str,
        "summary": {
            "title": str,
            "duration_days": int,
            "budget_estimate": str
        },
        "daily_plans": list,
        "raw_user_query": str,
        "amap_links": dict
    }


class MockLLMResponse:
    """模拟LLM响应"""
    def __init__(self, text: str):
        self.text = text


class MockLLMGeneration:
    """模拟LLM生成结果"""
    def __init__(self, text: str):
        self.generations = [[MockLLMResponse(text)]]


@pytest.fixture
def mock_llm_intent_response():
    """模拟意图分析LLM响应"""
    return MockLLMGeneration('''```json
{
    "destination": "北京",
    "duration_days": 3,
    "travel_type": ["历史古迹", "美食"],
    "budget": "medium",
    "transport": "public",
    "summary": "北京3日历史文化美食之旅"
}
```''')


@pytest.fixture
def mock_llm_planning_response():
    """模拟行程规划LLM响应"""
    return MockLLMGeneration('''```json
{
    "title": "北京3日历史文化美食之旅",
    "suggestion": "精心规划的北京深度游",
    "duration_days": 3,
    "budget_estimate": "中等预算",
    "daily_plans": [
        {
            "day_number": 1,
            "theme": "皇城文化探索",
            "pois": [
                {
                    "name": "故宫博物院",
                    "address": "北京市东城区景山前街4号",
                    "location": "116.397026,39.918058",
                    "type": "历史古迹",
                    "rating": 4.6,
                    "description": "明清两朝的皇家宫殿",
                    "visit_duration": 180,
                    "recommended_time": "上午",
                    "tags": ["历史", "文化", "必游"]
                }
            ],
            "route_summary": "天安门广场 -> 故宫博物院 -> 景山公园",
            "transportation": "步行+地铁",
            "estimated_cost": 200.0,
            "notes": "建议提前预约门票"
        }
    ]
}
```''')


# 测试数据清理
@pytest.fixture(autouse=True)
async def cleanup_test_data():
    """自动清理测试数据"""
    yield
    # 测试后清理
    try:
        if db_manager._initialized:
            db = await db_manager.mongodb.database
            # 清理测试数据
            await db.users.delete_many({"user_id": {"$regex": "^test_"}})
            await db.travel_itineraries.delete_many({"user_id": {"$regex": "^test_"}})
            await db.ai_interaction_logs.delete_many({"user_id": {"$regex": "^test_"}})
    except Exception:
        pass  # 忽略清理错误
