"""
旅行规划Agent相关的数据模型定义
"""
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class EventType(str, Enum):
    """SSE事件类型"""
    THOUGHT = "thought"
    TOOL_CALL = "tool_call"
    TOOL_RESULT = "tool_result"
    FINAL_RESULT = "final_result"
    ERROR = "error"


class StreamEvent(BaseModel):
    """流式事件模型"""
    event_id: str = Field(..., description="事件唯一ID")
    trace_id: str = Field(..., description="整个请求的追踪ID")
    event_type: EventType = Field(..., description="事件类型")
    payload: Dict[str, Any] = Field(..., description="事件负载数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="事件时间戳")


class TravelRequest(BaseModel):
    """旅行规划请求模型"""
    user_id: str = Field(..., description="用户ID")
    query: str = Field(..., description="用户查询内容")
    session_id: Optional[str] = Field(None, description="会话ID")


class UserProfile(BaseModel):
    """用户画像模型"""
    name: Optional[str] = None
    avatar: Optional[str] = None
    preferred_budget: str = Field(default="medium", description="预算偏好: low/medium/high")
    preferred_activities: List[str] = Field(default_factory=list, description="活动偏好")
    tags: List[str] = Field(default_factory=list, description="用户标签")


class User(BaseModel):
    """用户模型"""
    user_id: str
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    profile: UserProfile = Field(default_factory=UserProfile)


class Location(BaseModel):
    """地理位置模型"""
    name: str = Field(..., description="地点名称")
    address: str = Field(..., description="详细地址")
    longitude: float = Field(..., description="经度")
    latitude: float = Field(..., description="纬度")


class POI(BaseModel):
    """兴趣点模型"""
    id: str = Field(..., description="POI ID")
    name: str = Field(..., description="POI名称")
    address: str = Field(..., description="地址")
    location: Location = Field(..., description="地理位置")
    category: str = Field(..., description="类别")
    rating: Optional[float] = Field(None, description="评分")
    photos: List[str] = Field(default_factory=list, description="图片URL列表")
    business_hours: Optional[str] = Field(None, description="营业时间")
    phone: Optional[str] = Field(None, description="电话")
    tags: List[str] = Field(default_factory=list, description="标签")


class Activity(BaseModel):
    """活动模型"""
    time: str = Field(..., description="时间")
    venue: str = Field(..., description="地点")
    notes: Optional[str] = Field(None, description="备注")
    poi: Optional[POI] = Field(None, description="关联的POI信息")
    estimated_duration: Optional[str] = Field(None, description="预计时长")


class DayPlan(BaseModel):
    """单日行程模型"""
    day: int = Field(..., description="第几天")
    date: Optional[str] = Field(None, description="日期")
    theme: Optional[str] = Field(None, description="主题")
    activities: List[Activity] = Field(default_factory=list, description="活动列表")
    weather: Optional[str] = Field(None, description="天气信息")


class TripSummary(BaseModel):
    """行程摘要模型"""
    title: str = Field(..., description="行程标题")
    suggestion: Optional[str] = Field(None, description="建议说明")
    duration_days: int = Field(..., description="行程天数")
    budget_estimate: Optional[str] = Field(None, description="预算估算")


class TravelItinerary(BaseModel):
    """完整旅行行程模型"""
    trace_id: str = Field(..., description="追踪ID")
    user_id: str = Field(..., description="用户ID")
    created_at: datetime = Field(default_factory=datetime.now)
    summary: TripSummary = Field(..., description="行程摘要")
    daily_plans: List[DayPlan] = Field(default_factory=list, description="每日行程")
    raw_user_query: str = Field(..., description="原始用户查询")
    amap_links: Dict[str, str] = Field(default_factory=dict, description="高德地图链接")


class TripExecutionLog(BaseModel):
    """行程执行日志模型"""
    trace_id: str = Field(..., description="追踪ID")
    user_id: str = Field(..., description="用户ID")
    created_at: datetime = Field(default_factory=datetime.now)
    log: List[StreamEvent] = Field(default_factory=list, description="执行事件列表")


class IntentAnalysis(BaseModel):
    """意图分析结果模型"""
    origin: Optional[str] = Field(None, description="出发地")
    destination: Optional[str] = Field(None, description="目的地")
    transport_mode: str = Field(default="driving", description="出行方式")
    dates: List[str] = Field(default_factory=list, description="日期列表")
    duration_days: int = Field(default=1, description="行程天数")
    budget_level: str = Field(default="medium", description="预算水平")
    group_type: str = Field(default="solo", description="出行类型: solo/couple/family/friends")
    interests: List[str] = Field(default_factory=list, description="兴趣标签")
    special_requirements: List[str] = Field(default_factory=list, description="特殊需求")


class ToolCallPlan(BaseModel):
    """工具调用计划模型"""
    tool_name: str = Field(..., description="工具名称")
    parameters: Dict[str, Any] = Field(..., description="调用参数")
    priority: int = Field(default=1, description="优先级")
    depends_on: List[str] = Field(default_factory=list, description="依赖的工具")


class WeatherInfo(BaseModel):
    """天气信息模型"""
    city: str = Field(..., description="城市")
    weather: str = Field(..., description="天气状况")
    temperature: str = Field(..., description="温度")
    wind: Optional[str] = Field(None, description="风力")
    humidity: Optional[str] = Field(None, description="湿度")
    date: str = Field(..., description="日期")


class RouteInfo(BaseModel):
    """路线信息模型"""
    origin: Location = Field(..., description="起点")
    destination: Location = Field(..., description="终点")
    distance: str = Field(..., description="距离")
    duration: str = Field(..., description="时长")
    route_description: Optional[str] = Field(None, description="路线描述")
    tolls: Optional[str] = Field(None, description="过路费")