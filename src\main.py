"""
FastAPI 应用入口点

这是整个应用的主入口，负责初始化FastAPI应用、配置路由、中间件等。
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
from src.core.config import settings
from src.core.logger import get_logger
from src.core.database import db_manager
from src.api.travel_planner.router import router as travel_planner_router

logger = get_logger('main')


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("Starting AutoPilot AI application...")

    # 尝试初始化数据库连接（允许失败）
    try:
        await db_manager.initialize()
        logger.info("Database connections initialized successfully")
    except Exception as e:
        logger.warning(f"Database initialization failed: {str(e)}")
        logger.warning("Application will continue without database features")

    # 导入工具模块以注册工具
    try:
        import src.tools.amap_tools
        import src.tools.memory_tools
        logger.info("Tools registered successfully")
    except Exception as e:
        logger.error(f"Tool registration failed: {str(e)}")

    yield

    # 关闭时清理
    logger.info("Shutting down AutoPilot AI application...")
    try:
        await db_manager.cleanup()
        logger.info("Database connections closed")
    except Exception as e:
        logger.warning(f"Database cleanup failed: {str(e)}")


# 创建FastAPI应用实例
app = FastAPI(
    title="AutoPilot AI - 智能旅行规划助手",
    description="基于高德地图API和LLM的智能旅行规划系统",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 注册路由
app.include_router(travel_planner_router)

@app.get("/")
async def root():
    """根路径，返回API基本信息"""
    return {
        "message": "Welcome to AutoPilot AI - 智能旅行规划助手",
        "version": "0.1.0",
        "docs": "/docs",
        "frontend": "/static/index.html"
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    # 检查数据库连接
    db_health = await db_manager.health_check()
    
    return {
        "status": "healthy" if all(db_health.values()) else "degraded",
        "message": "AutoPilot AI is running",
        "database": db_health
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
