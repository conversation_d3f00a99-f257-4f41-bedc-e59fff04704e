"""
工具注册器模块

提供统一的工具注册和管理机制，支持动态工具发现和注册。
"""
import inspect
from typing import Dict, List, Callable, Any, Optional
from functools import wraps
from src.core.logger import get_logger

logger = get_logger('tools.registry')


class ToolRegistry:
    """工具注册器，管理所有可用的工具函数"""
    
    def __init__(self):
        self._tools: Dict[str, Dict[str, Any]] = {}
        
    def register(
        self, 
        name: str, 
        description: str = "",
        category: str = "general",
        parameters_schema: Optional[Dict] = None
    ):
        """
        装饰器：注册工具函数
        
        Args:
            name: 工具名称
            description: 工具描述
            category: 工具分类
            parameters_schema: 参数模式定义
        """
        def decorator(func: Callable):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                try:
                    logger.info(f"Executing tool: {name}")
                    result = await func(*args, **kwargs)
                    logger.info(f"Tool {name} executed successfully")
                    return result
                except Exception as e:
                    logger.error(f"Tool {name} execution failed: {str(e)}")
                    raise
            
            # 获取函数签名
            sig = inspect.signature(func)
            
            # 注册工具
            self._tools[name] = {
                'function': wrapper,
                'original_function': func,
                'description': description,
                'category': category,
                'parameters_schema': parameters_schema or self._extract_schema(sig),
                'signature': sig
            }
            
            logger.info(f"Registered tool: {name} in category: {category}")
            return wrapper
        
        return decorator
    
    def _extract_schema(self, signature: inspect.Signature) -> Dict:
        """从函数签名提取参数模式"""
        schema = {
            "type": "object",
            "properties": {},
            "required": []
        }
        
        for param_name, param in signature.parameters.items():
            if param_name == 'self':
                continue
                
            param_info = {"type": "string"}  # 默认类型
            
            # 处理类型注解
            if param.annotation != inspect.Parameter.empty:
                if param.annotation == int:
                    param_info["type"] = "integer"
                elif param.annotation == float:
                    param_info["type"] = "number"
                elif param.annotation == bool:
                    param_info["type"] = "boolean"
                elif param.annotation == list:
                    param_info["type"] = "array"
                elif param.annotation == dict:
                    param_info["type"] = "object"
            
            # 处理默认值
            if param.default == inspect.Parameter.empty:
                schema["required"].append(param_name)
            else:
                param_info["default"] = param.default
            
            schema["properties"][param_name] = param_info
        
        return schema
    
    def get_tool(self, name: str) -> Optional[Dict[str, Any]]:
        """获取指定工具"""
        return self._tools.get(name)
    
    def get_tools_by_category(self, category: str) -> Dict[str, Dict[str, Any]]:
        """获取指定分类的所有工具"""
        return {
            name: tool for name, tool in self._tools.items()
            if tool['category'] == category
        }
    
    def list_tools(self) -> List[str]:
        """列出所有工具名称"""
        return list(self._tools.keys())
    
    def get_tool_info(self, name: str) -> Optional[Dict[str, Any]]:
        """获取工具信息（不包含函数对象）"""
        tool = self._tools.get(name)
        if tool:
            return {
                'name': name,
                'description': tool['description'],
                'category': tool['category'],
                'parameters_schema': tool['parameters_schema']
            }
        return None
    
    def get_all_tools_info(self) -> List[Dict[str, Any]]:
        """获取所有工具信息"""
        return [self.get_tool_info(name) for name in self._tools.keys()]
    
    async def execute_tool(self, name: str, **kwargs) -> Any:
        """执行指定工具"""
        tool = self._tools.get(name)
        if not tool:
            raise ValueError(f"Tool '{name}' not found")
        
        return await tool['function'](**kwargs)


# 全局工具注册器实例
tool_registry = ToolRegistry()
