# AutoPilot AI - Android API 配置
# 专门为Android应用提供的后端API配置

# 应用基础配置
app:
  name: "AutoPilot AI Travel Planner API"
  version: "1.0.0"
  debug: false
  cors_enabled: true
  allowed_origins: ["*"]  # 生产环境中应该限制具体域名

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  timeout: 30

# LLM配置 (使用您的配置)
basic_llm:
  model: "glm-4-flash"
  base_url: "https://open.bigmodel.cn/api/paas/v4/"
  api_key: "${BASIC_LLM_API_KEY}"  # 从环境变量获取
  timeout: 30
  max_retries: 3

reasoning_llm:
  model: "glm-z1-flash"
  base_url: "https://open.bigmodel.cn/api/paas/v4/"
  api_key: "${REASONING_LLM_API_KEY}"  # 从环境变量获取
  timeout: 60
  max_retries: 3

# 高德地图配置
amap:
  base_url: "https://mcp.amap.com/sse"
  api_key: "${AMAP_API_KEY}"  # 从环境变量获取
  timeout: 30
  max_retries: 3

# MySQL配置 (您的数据库)
mysql:
  host: "***********"
  port: 19090
  username: "root"
  password: "Fsit#2024"
  database: "travel_planner"
  charset: "utf8mb4"
  pool_size: 5
  pool_timeout: 30

# MongoDB配置 (您的数据库)
mongodb:
  host: "***********"
  port: 27017
  username: "admin"
  password: "m25uids*g"
  database: "dh_platform_data"
  auth_source: "admin"
  pool_size: 10
  timeout: 10

# Redis配置 (您的数据库)
redis:
  host: "***********"
  port: 5182
  password: "kLKe3NFM4RZMgXhA"
  database: 0
  pool_size: 10
  timeout: 5
  expiry_time: 86400  # 24小时过期（秒）

# 日志配置
logging:
  level: "INFO"
  format: "json"
  file_enabled: true
  file_path: "logs/android_api.log"
  max_file_size: "10MB"
  backup_count: 5

# API限制配置
rate_limiting:
  enabled: true
  requests_per_minute: 60
  requests_per_hour: 1000
  burst_size: 10

# 安全配置
security:
  api_key_required: false  # 如果需要API密钥认证，设置为true
  allowed_ips: []  # 空数组表示允许所有IP
  max_request_size: "10MB"

# 功能开关
features:
  database_required: false  # 设置为false允许无数据库运行
  streaming_enabled: true
  memory_system_enabled: true
  amap_tools_enabled: true
  mock_mode: false  # 设置为true使用模拟数据

# Android专用配置
android:
  api_version: "v1"
  response_format: "json"
  include_debug_info: false
  max_response_size: "5MB"
  
  # 支持的功能列表
  supported_features:
    - "travel_planning"
    - "poi_search"
    - "route_planning"
    - "weather_info"
    - "user_history"
    - "streaming_response"
  
  # 响应优化
  response_optimization:
    compress_large_responses: true
    include_metadata: true
    cache_static_data: true

# 监控配置
monitoring:
  health_check_enabled: true
  metrics_enabled: true
  performance_tracking: true
  error_reporting: true

# 缓存配置
cache:
  enabled: true
  default_ttl: 3600  # 1小时
  max_size: "100MB"
  
  # 缓存策略
  strategies:
    poi_search: 1800    # 30分钟
    weather_info: 600   # 10分钟
    route_planning: 3600 # 1小时
    user_profile: 86400  # 24小时
