"""
高德地图MCP工具集成

基于高德地图API提供15个核心工具，支持地点搜索、路径规划、周边服务等功能。
"""
import aiohttp
import asyncio
from typing import Dict, List, Any, Optional
from urllib.parse import urlencode
import yaml
from src.core.logger import get_logger
from src.tools.registry import tool_registry
from src.tools.sse_output import sse_manager

logger = get_logger('tools.amap')

# 加载配置
with open('config/default.yaml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

AMAP_CONFIG = config.get('amap', {})
BASE_URL = AMAP_CONFIG.get('base_url', 'https://mcp.amap.com/sse')
API_KEY = AMAP_CONFIG.get('api_key', '')
TIMEOUT = AMAP_CONFIG.get('timeout', 30)
MAX_RETRIES = AMAP_CONFIG.get('max_retries', 3)


class AmapAPIClient:
    """高德地图API客户端"""
    
    def __init__(self):
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=TIMEOUT)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """发送API请求"""
        params['key'] = API_KEY
        url = f"{BASE_URL}/{endpoint}"
        
        for attempt in range(MAX_RETRIES):
            try:
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.debug(f"Amap API call successful: {endpoint}")
                        return result
                    else:
                        logger.warning(f"Amap API call failed: {response.status}")
                        if attempt == MAX_RETRIES - 1:
                            raise Exception(f"API call failed with status {response.status}")
            except Exception as e:
                if attempt == MAX_RETRIES - 1:
                    logger.error(f"Amap API call failed after {MAX_RETRIES} attempts: {str(e)}")
                    raise
                await asyncio.sleep(2 ** attempt)  # 指数退避
        
        raise Exception("Max retries exceeded")


# 创建全局客户端实例
amap_client = AmapAPIClient()


@tool_registry.register(
    name="search_poi",
    description="搜索兴趣点(POI)，如景点、餐厅、酒店等",
    category="amap"
)
async def search_poi(
    keywords: str,
    city: str = "",
    types: str = "",
    page: int = 1,
    offset: int = 20
) -> Dict[str, Any]:
    """
    搜索兴趣点
    
    Args:
        keywords: 搜索关键词
        city: 城市名称
        types: POI类型代码
        page: 页码
        offset: 每页数量
    """
    async with AmapAPIClient() as client:
        params = {
            'keywords': keywords,
            'city': city,
            'types': types,
            'page': page,
            'offset': offset
        }
        return await client._make_request('place/text', params)


@tool_registry.register(
    name="search_around",
    description="搜索指定位置周边的POI",
    category="amap"
)
async def search_around(
    location: str,
    keywords: str = "",
    types: str = "",
    radius: int = 1000,
    page: int = 1,
    offset: int = 20
) -> Dict[str, Any]:
    """
    搜索周边POI
    
    Args:
        location: 中心点坐标 "经度,纬度"
        keywords: 搜索关键词
        types: POI类型代码
        radius: 搜索半径(米)
        page: 页码
        offset: 每页数量
    """
    async with AmapAPIClient() as client:
        params = {
            'location': location,
            'keywords': keywords,
            'types': types,
            'radius': radius,
            'page': page,
            'offset': offset
        }
        return await client._make_request('place/around', params)


@tool_registry.register(
    name="geocode",
    description="地理编码：将地址转换为坐标",
    category="amap"
)
async def geocode(address: str, city: str = "") -> Dict[str, Any]:
    """
    地理编码
    
    Args:
        address: 地址
        city: 城市名称
    """
    async with AmapAPIClient() as client:
        params = {
            'address': address,
            'city': city
        }
        return await client._make_request('geocode/geo', params)


@tool_registry.register(
    name="regeocode",
    description="逆地理编码：将坐标转换为地址",
    category="amap"
)
async def regeocode(location: str, radius: int = 1000, extensions: str = "base") -> Dict[str, Any]:
    """
    逆地理编码
    
    Args:
        location: 坐标 "经度,纬度"
        radius: 搜索半径(米)
        extensions: 返回结果详细程度
    """
    async with AmapAPIClient() as client:
        params = {
            'location': location,
            'radius': radius,
            'extensions': extensions
        }
        return await client._make_request('geocode/regeo', params)


@tool_registry.register(
    name="route_planning",
    description="路径规划：计算两点间的路线",
    category="amap"
)
async def route_planning(
    origin: str,
    destination: str,
    strategy: int = 0,
    waypoints: str = "",
    avoidpolygons: str = "",
    avoidroad: str = ""
) -> Dict[str, Any]:
    """
    路径规划
    
    Args:
        origin: 起点坐标 "经度,纬度"
        destination: 终点坐标 "经度,纬度"
        strategy: 路径策略 0-速度优先 1-费用优先 2-距离优先 3-不走高速
        waypoints: 途经点坐标
        avoidpolygons: 避让区域
        avoidroad: 避让道路
    """
    async with AmapAPIClient() as client:
        params = {
            'origin': origin,
            'destination': destination,
            'strategy': strategy,
            'waypoints': waypoints,
            'avoidpolygons': avoidpolygons,
            'avoidroad': avoidroad
        }
        return await client._make_request('direction/driving', params)


@tool_registry.register(
    name="distance_matrix",
    description="距离矩阵：计算多个起终点间的距离和时间",
    category="amap"
)
async def distance_matrix(
    origins: str,
    destinations: str,
    type: int = 1
) -> Dict[str, Any]:
    """
    距离矩阵
    
    Args:
        origins: 起点坐标列表，用|分隔
        destinations: 终点坐标列表，用|分隔
        type: 路径计算方式 1-直线距离 3-驾车路径
    """
    async with AmapAPIClient() as client:
        params = {
            'origins': origins,
            'destinations': destinations,
            'type': type
        }
        return await client._make_request('distance', params)


@tool_registry.register(
    name="traffic_info",
    description="获取实时交通信息",
    category="amap"
)
async def traffic_info(
    rectangle: str,
    level: int = 1,
    extensions: str = "base"
) -> Dict[str, Any]:
    """
    获取交通态势信息

    Args:
        rectangle: 矩形区域范围 "左下角经度,左下角纬度;右上角经度,右上角纬度"
        level: 道路等级 0-全部 1-高速+城市快速路+国道 2-高速+城市快速路+国道+省道
        extensions: 返回结果详细程度
    """
    async with AmapAPIClient() as client:
        params = {
            'rectangle': rectangle,
            'level': level,
            'extensions': extensions
        }
        return await client._make_request('traffic/status/rectangle', params)


@tool_registry.register(
    name="weather_info",
    description="获取天气信息",
    category="amap"
)
async def weather_info(
    city: str,
    extensions: str = "base"
) -> Dict[str, Any]:
    """
    获取天气信息

    Args:
        city: 城市编码或城市名称
        extensions: base-实况天气 all-预报天气
    """
    async with AmapAPIClient() as client:
        params = {
            'city': city,
            'extensions': extensions
        }
        return await client._make_request('weather/weatherInfo', params)


@tool_registry.register(
    name="administrative_district",
    description="获取行政区域信息",
    category="amap"
)
async def administrative_district(
    keywords: str = "",
    subdistrict: int = 1,
    page: int = 1,
    offset: int = 20,
    extensions: str = "base"
) -> Dict[str, Any]:
    """
    获取行政区域信息

    Args:
        keywords: 查询关键词
        subdistrict: 子级行政区 0-不返回 1-返回下一级 2-返回下两级 3-返回下三级
        page: 页码
        offset: 每页数量
        extensions: 返回结果详细程度
    """
    async with AmapAPIClient() as client:
        params = {
            'keywords': keywords,
            'subdistrict': subdistrict,
            'page': page,
            'offset': offset,
            'extensions': extensions
        }
        return await client._make_request('config/district', params)


@tool_registry.register(
    name="ip_location",
    description="IP定位：根据IP地址获取位置信息",
    category="amap"
)
async def ip_location(ip: str = "") -> Dict[str, Any]:
    """
    IP定位

    Args:
        ip: IP地址，不填则自动获取请求IP
    """
    async with AmapAPIClient() as client:
        params = {'ip': ip} if ip else {}
        return await client._make_request('ip', params)


@tool_registry.register(
    name="static_map",
    description="获取静态地图图片",
    category="amap"
)
async def static_map(
    location: str,
    zoom: int = 10,
    size: str = "400*300",
    scale: int = 1,
    markers: str = "",
    paths: str = "",
    traffic: int = 0
) -> Dict[str, Any]:
    """
    获取静态地图

    Args:
        location: 地图中心点坐标 "经度,纬度"
        zoom: 地图缩放级别 3-18
        size: 地图大小 "宽*高"
        scale: 普通/高清 1-普通 2-高清
        markers: 标注点
        paths: 路径
        traffic: 交通路况 0-不显示 1-显示
    """
    async with AmapAPIClient() as client:
        params = {
            'location': location,
            'zoom': zoom,
            'size': size,
            'scale': scale,
            'markers': markers,
            'paths': paths,
            'traffic': traffic
        }
        return await client._make_request('staticmap', params)


@tool_registry.register(
    name="inputtips",
    description="输入提示：根据用户输入提供搜索建议",
    category="amap"
)
async def inputtips(
    keywords: str,
    type: str = "",
    location: str = "",
    city: str = "",
    citylimit: bool = False
) -> Dict[str, Any]:
    """
    输入提示

    Args:
        keywords: 查询关键词
        type: 返回的数据类型 all-返回所有数据类型 poi-返回POI类型数据 bus-返回公交站点数据 busline-返回公交线路数据
        location: 坐标 "经度,纬度"
        city: 城市
        citylimit: 仅返回指定城市数据
    """
    async with AmapAPIClient() as client:
        params = {
            'keywords': keywords,
            'type': type,
            'location': location,
            'city': city,
            'citylimit': citylimit
        }
        return await client._make_request('assistant/inputtips', params)


@tool_registry.register(
    name="bus_route",
    description="公交路线规划",
    category="amap"
)
async def bus_route(
    origin: str,
    destination: str,
    city: str,
    cityd: str = "",
    strategy: int = 0,
    nightflag: int = 0,
    date: str = "",
    time: str = ""
) -> Dict[str, Any]:
    """
    公交路线规划

    Args:
        origin: 起点坐标 "经度,纬度"
        destination: 终点坐标 "经度,纬度"
        city: 起点城市
        cityd: 终点城市
        strategy: 公交换乘策略 0-最快捷模式 1-最经济模式 2-最少换乘模式 3-最少步行模式 5-不乘地铁模式
        nightflag: 是否计算夜班车 0-不计算 1-计算
        date: 出发日期
        time: 出发时间
    """
    async with AmapAPIClient() as client:
        params = {
            'origin': origin,
            'destination': destination,
            'city': city,
            'cityd': cityd,
            'strategy': strategy,
            'nightflag': nightflag,
            'date': date,
            'time': time
        }
        return await client._make_request('direction/transit/integrated', params)


@tool_registry.register(
    name="walking_route",
    description="步行路线规划",
    category="amap"
)
async def walking_route(
    origin: str,
    destination: str,
    isindoor: int = 0
) -> Dict[str, Any]:
    """
    步行路线规划

    Args:
        origin: 起点坐标 "经度,纬度"
        destination: 终点坐标 "经度,纬度"
        isindoor: 是否室内 0-室外 1-室内
    """
    async with AmapAPIClient() as client:
        params = {
            'origin': origin,
            'destination': destination,
            'isindoor': isindoor
        }
        return await client._make_request('direction/walking', params)


@tool_registry.register(
    name="cycling_route",
    description="骑行路线规划",
    category="amap"
)
async def cycling_route(
    origin: str,
    destination: str,
    alternative_route: int = 0
) -> Dict[str, Any]:
    """
    骑行路线规划

    Args:
        origin: 起点坐标 "经度,纬度"
        destination: 终点坐标 "经度,纬度"
        alternative_route: 备选路线 0-只返回一条路线 1-返回备选路线
    """
    async with AmapAPIClient() as client:
        params = {
            'origin': origin,
            'destination': destination,
            'alternative_route': alternative_route
        }
        return await client._make_request('direction/bicycling', params)


@tool_registry.register(
    name="truck_route",
    description="货车路线规划",
    category="amap"
)
async def truck_route(
    origin: str,
    destination: str,
    size: int = 1,
    height: float = 1.6,
    width: float = 2.5,
    load: float = 0.9,
    weight: float = 2.5,
    axis: int = 2,
    province: str = "",
    number: str = ""
) -> Dict[str, Any]:
    """
    货车路线规划

    Args:
        origin: 起点坐标 "经度,纬度"
        destination: 终点坐标 "经度,纬度"
        size: 货车尺寸 1-微型货车 2-轻型货车 3-中型货车 4-重型货车
        height: 车辆高度(米)
        width: 车辆宽度(米)
        load: 核定载重(吨)
        weight: 车辆总重(吨)
        axis: 车轴数
        province: 车牌省份
        number: 车牌号码
    """
    async with AmapAPIClient() as client:
        params = {
            'origin': origin,
            'destination': destination,
            'size': size,
            'height': height,
            'width': width,
            'load': load,
            'weight': weight,
            'axis': axis,
            'province': province,
            'number': number
        }
        return await client._make_request('direction/truck', params)


# 工具使用示例和帮助函数
async def get_amap_tools_info() -> List[Dict[str, Any]]:
    """获取所有高德地图工具信息"""
    return tool_registry.get_tools_by_category("amap")


async def execute_amap_tool(tool_name: str, **kwargs) -> Any:
    """执行高德地图工具"""
    return await tool_registry.execute_tool(tool_name, **kwargs)
