"""
数据模型定义

包含所有Pydantic数据模型的定义。
"""

# 导入旅行规划相关模型
from .travel_models import (
    EventType, StreamEvent, TravelRequest, UserProfile, User,
    Location, POI, Activity, DayPlan, TripSummary, TravelItinerary,
    TripExecutionLog, IntentAnalysis, ToolCallPlan, WeatherInfo, RouteInfo
)

__all__ = [
    "EventType", "StreamEvent", "TravelRequest", "UserProfile", "User",
    "Location", "POI", "Activity", "DayPlan", "TripSummary", "TravelItinerary",
    "TripExecutionLog", "IntentAnalysis", "ToolCallPlan", "WeatherInfo", "RouteInfo"
]