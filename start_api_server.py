#!/usr/bin/env python3
"""
独立API服务器启动脚本

专门为Android应用提供后端API服务，支持独立运行。
"""
import sys
import os
import asyncio
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.logger import get_logger
from src.core.config import get_config

logger = get_logger('api_server')


def check_environment():
    """检查运行环境"""
    logger.info("Checking environment...")
    
    # 检查配置文件
    config_file = project_root / "config" / "default.yaml"
    if not config_file.exists():
        logger.error("Configuration file not found: config/default.yaml")
        return False
    
    # 检查必要的目录
    directories = ["logs", "static"]
    for directory in directories:
        dir_path = project_root / directory
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory}")
    
    logger.info("Environment check completed")
    return True


async def test_database_connection():
    """测试数据库连接（可选）"""
    logger.info("Testing database connections...")
    
    try:
        from src.core.database import db_manager
        
        # 尝试连接数据库
        await db_manager.initialize()
        health = await db_manager.health_check()
        
        logger.info("Database connection test results:")
        for service, status in health.items():
            status_text = "✓ Connected" if status else "✗ Failed"
            logger.info(f"  {service}: {status_text}")
        
        await db_manager.cleanup()
        
        return any(health.values())  # 至少有一个数据库连接成功
        
    except Exception as e:
        logger.warning(f"Database connection test failed: {str(e)}")
        logger.warning("API will run without database features")
        return False


def create_minimal_app():
    """创建最小化的FastAPI应用（无数据库依赖）"""
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import JSONResponse
    
    app = FastAPI(
        title="AutoPilot AI - Travel Planner API",
        description="智能旅行规划API服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境中应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    async def root():
        return {"message": "AutoPilot AI Travel Planner API", "status": "running"}
    
    @app.get("/health")
    async def health_check():
        """健康检查端点"""
        return {
            "status": "healthy",
            "service": "AutoPilot AI Travel Planner",
            "version": "1.0.0",
            "database": {
                "mongodb": False,  # 简化版本不依赖数据库
                "redis": False
            }
        }
    
    @app.post("/travel-planner/plan")
    async def create_travel_plan(request: dict):
        """简化的旅行规划接口"""
        try:
            user_id = request.get("user_id", "anonymous")
            query = request.get("query", "")
            
            if not query:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "error": "Query is required"}
                )
            
            # 模拟旅行规划响应
            mock_response = {
                "success": True,
                "trace_id": f"trace_{user_id}_{hash(query) % 10000}",
                "data": {
                    "itinerary": {
                        "user_id": user_id,
                        "summary": {
                            "title": "智能旅行规划",
                            "suggestion": "基于您的需求生成的旅行建议",
                            "duration_days": 2,
                            "budget_estimate": "中等预算"
                        },
                        "daily_plans": [
                            {
                                "day_number": 1,
                                "theme": "探索之旅",
                                "pois": [
                                    {
                                        "name": "推荐景点1",
                                        "address": "示例地址",
                                        "type": "景点",
                                        "rating": 4.5,
                                        "description": "基于您的查询推荐的景点"
                                    }
                                ]
                            }
                        ]
                    },
                    "intent_analysis": {
                        "destination": "待分析",
                        "duration_days": 2,
                        "travel_type": ["观光"],
                        "budget": "medium"
                    }
                },
                "message": "这是一个简化的演示响应。完整功能需要配置LLM和地图API。"
            }
            
            return mock_response
            
        except Exception as e:
            logger.error(f"Travel planning failed: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "error": "Internal server error",
                    "message": str(e)
                }
            )
    
    @app.get("/travel-planner/history/{user_id}")
    async def get_user_history(user_id: str):
        """获取用户历史记录（模拟）"""
        return {
            "success": True,
            "user_id": user_id,
            "count": 0,
            "itineraries": [],
            "message": "历史记录功能需要数据库支持"
        }
    
    return app


def run_api_server(host="0.0.0.0", port=8000, use_full_app=True):
    """运行API服务器"""
    logger.info(f"Starting API server on {host}:{port}")
    
    if use_full_app:
        # 使用完整的应用（包含数据库功能）
        app_module = "src.main:app"
        logger.info("Using full application with database features")
    else:
        # 使用简化的应用（无数据库依赖）
        app = create_minimal_app()
        logger.info("Using minimal application without database dependencies")
        
        # 直接运行简化应用
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )
        return
    
    # 运行完整应用
    uvicorn.run(
        app_module,
        host=host,
        port=port,
        log_level="info",
        access_log=True,
        reload=False  # 生产模式不使用reload
    )


async def main():
    """主函数"""
    print("🚀 AutoPilot AI - API Server for Android")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        print("❌ Environment check failed")
        sys.exit(1)
    
    # 测试数据库连接
    db_available = await test_database_connection()
    
    print(f"\n📊 Database Status: {'Available' if db_available else 'Not Available'}")
    print(f"🔧 API Mode: {'Full Features' if db_available else 'Basic Mode'}")
    
    # 询问用户选择
    print("\n选择运行模式:")
    print("1. 完整模式 (需要数据库和API配置)")
    print("2. 基础模式 (无需数据库，提供模拟响应)")
    print("3. 自动选择 (根据数据库可用性)")
    
    choice = input("请选择 (1-3, 默认3): ").strip() or "3"
    
    use_full_app = True
    if choice == "1":
        use_full_app = True
        print("🔥 启动完整模式...")
    elif choice == "2":
        use_full_app = False
        print("⚡ 启动基础模式...")
    else:  # choice == "3"
        use_full_app = db_available
        mode = "完整模式" if use_full_app else "基础模式"
        print(f"🤖 自动选择: {mode}")
    
    # 启动服务器
    try:
        run_api_server(use_full_app=use_full_app)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
