"""
数据库连接管理器

提供MongoDB和其他数据库的连接管理功能。
"""
import asyncio
from typing import Optional, Dict, Any
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
import yaml
from src.core.logger import get_logger

logger = get_logger('core.database')

# 加载配置
with open('config/default.yaml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)


class MongoDBManager:
    """MongoDB连接管理器"""
    
    def __init__(self):
        self._client: Optional[AsyncIOMotorClient] = None
        self._database: Optional[AsyncIOMotorDatabase] = None
        self._config = config.get('mongodb', {})
        self._connection_string = self._build_connection_string()
    
    def _build_connection_string(self) -> str:
        """构建MongoDB连接字符串"""
        host = self._config.get('host', 'localhost')
        port = self._config.get('port', 27017)
        username = self._config.get('username', '')
        password = self._config.get('password', '')
        database = self._config.get('database', 'dh_platform_data')
        
        if username and password:
            return f"mongodb://{username}:{password}@{host}:{port}/{database}"
        else:
            return f"mongodb://{host}:{port}/{database}"
    
    async def connect(self):
        """建立数据库连接"""
        if self._client is None:
            try:
                self._client = AsyncIOMotorClient(
                    self._connection_string,
                    serverSelectionTimeoutMS=5000,  # 5秒超时
                    maxPoolSize=10,  # 最大连接池大小
                    minPoolSize=1,   # 最小连接池大小
                )
                
                # 测试连接
                await self._client.admin.command('ping')
                
                self._database = self._client[self._config.get('database', 'dh_platform_data')]
                logger.info("MongoDB connection established successfully")
                
            except Exception as e:
                logger.error(f"Failed to connect to MongoDB: {str(e)}")
                raise
    
    async def disconnect(self):
        """关闭数据库连接"""
        if self._client:
            self._client.close()
            self._client = None
            self._database = None
            logger.info("MongoDB connection closed")
    
    @property
    def database(self) -> AsyncIOMotorDatabase:
        """获取数据库实例"""
        if self._database is None:
            raise RuntimeError("Database not connected. Call connect() first.")
        return self._database
    
    @property
    def client(self) -> AsyncIOMotorClient:
        """获取客户端实例"""
        if self._client is None:
            raise RuntimeError("Database not connected. Call connect() first.")
        return self._client
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if self._client:
                await self._client.admin.command('ping')
                return True
            return False
        except Exception as e:
            logger.error(f"MongoDB health check failed: {str(e)}")
            return False


class DatabaseManager:
    """统一数据库管理器"""
    
    def __init__(self):
        self.mongodb = MongoDBManager()
        self._initialized = False
    
    async def initialize(self):
        """初始化所有数据库连接"""
        if not self._initialized:
            await self.mongodb.connect()
            self._initialized = True
            logger.info("Database manager initialized")
    
    async def cleanup(self):
        """清理所有数据库连接"""
        if self._initialized:
            await self.mongodb.disconnect()
            self._initialized = False
            logger.info("Database manager cleaned up")
    
    async def health_check(self) -> Dict[str, bool]:
        """所有数据库健康检查"""
        return {
            "mongodb": await self.mongodb.health_check()
        }


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 便捷函数
async def get_mongodb() -> AsyncIOMotorDatabase:
    """获取MongoDB数据库实例"""
    if not db_manager._initialized:
        await db_manager.initialize()
    return db_manager.mongodb.database


async def get_mongo_client() -> AsyncIOMotorClient:
    """获取MongoDB客户端实例"""
    if not db_manager._initialized:
        await db_manager.initialize()
    return db_manager.mongodb.client


# 上下文管理器
class DatabaseContext:
    """数据库上下文管理器"""
    
    async def __aenter__(self):
        await db_manager.initialize()
        return db_manager
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # 通常不在这里关闭连接，因为可能有其他地方在使用
        pass


# 装饰器
def with_database(func):
    """数据库连接装饰器"""
    async def wrapper(*args, **kwargs):
        await db_manager.initialize()
        return await func(*args, **kwargs)
    return wrapper
