"""
系统集成测试

端到端测试整个旅行规划流程。
"""
import pytest
import asyncio
import json
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock
from src.main import app
from src.agents.travel_planner_agent import TravelPlannerAgent
from src.memory.memory_manager import memory_manager
from src.tools.registry import tool_registry


class TestSystemIntegration:
    """系统集成测试类"""
    
    @pytest.mark.asyncio
    async def test_complete_travel_planning_flow(self, async_client: AsyncClient, setup_database):
        """测试完整的旅行规划流程"""
        # 1. 创建用户画像
        user_data = {
            "user_id": "integration_test_user",
            "name": "集成测试用户",
            "preferred_budget": "medium",
            "preferred_activities": ["历史古迹", "美食"],
            "tags": ["文化游"]
        }
        
        # 保存用户画像
        from src.tools.memory_tools import save_user_profile
        profile_result = await save_user_profile(**user_data)
        assert profile_result["success"] is True
        
        # 2. 发起旅行规划请求
        planning_request = {
            "user_id": "integration_test_user",
            "query": "我想去北京玩3天，喜欢历史古迹和美食，预算中等",
            "context": {
                "travel_dates": {
                    "start_date": "2024-07-01",
                    "duration": 3
                }
            }
        }
        
        # 模拟外部API调用
        mock_amap_response = {
            "status": "1",
            "pois": [
                {
                    "name": "故宫博物院",
                    "address": "北京市东城区景山前街4号",
                    "location": "116.397026,39.918058",
                    "type": "风景名胜",
                    "rating": "4.6"
                },
                {
                    "name": "全聚德烤鸭店",
                    "address": "北京市东城区前门大街30号",
                    "location": "116.397477,39.903738",
                    "type": "餐饮服务",
                    "rating": "4.3"
                }
            ]
        }
        
        mock_weather_response = {
            "status": "1",
            "lives": [
                {
                    "city": "北京市",
                    "weather": "晴",
                    "temperature": "25",
                    "humidity": "45"
                }
            ]
        }
        
        mock_llm_intent_response = '''```json
{
    "destination": "北京",
    "duration_days": 3,
    "travel_type": ["历史古迹", "美食"],
    "budget": "medium",
    "transport": "public",
    "summary": "北京3日历史文化美食之旅",
    "original_query": "我想去北京玩3天，喜欢历史古迹和美食，预算中等"
}
```'''
        
        mock_llm_planning_response = '''```json
{
    "title": "北京3日历史文化美食之旅",
    "suggestion": "精心规划的北京深度游，融合历史文化与地道美食",
    "duration_days": 3,
    "budget_estimate": "中等预算 (约2000-3000元)",
    "daily_plans": [
        {
            "day_number": 1,
            "theme": "皇城文化探索",
            "pois": [
                {
                    "name": "故宫博物院",
                    "address": "北京市东城区景山前街4号",
                    "location": "116.397026,39.918058",
                    "type": "历史古迹",
                    "rating": 4.6,
                    "description": "明清两朝的皇家宫殿，中华文明的瑰宝",
                    "visit_duration": 180,
                    "recommended_time": "上午9:00-12:00",
                    "tags": ["历史", "文化", "必游", "世界遗产"]
                },
                {
                    "name": "全聚德烤鸭店",
                    "address": "北京市东城区前门大街30号",
                    "location": "116.397477,39.903738",
                    "type": "美食",
                    "rating": 4.3,
                    "description": "百年老字号，正宗北京烤鸭",
                    "visit_duration": 90,
                    "recommended_time": "晚上18:00-19:30",
                    "tags": ["美食", "老字号", "烤鸭", "特色"]
                }
            ],
            "route_summary": "天安门广场 -> 故宫博物院 -> 前门大街 -> 全聚德",
            "transportation": "步行+地铁1号线",
            "estimated_cost": 350.0,
            "notes": "建议提前预约故宫门票，避开周末高峰期"
        }
    ]
}
```'''
        
        # 模拟LLM和API调用
        with patch('aiohttp.ClientSession.get') as mock_get, \
             patch('src.agents.travel_planner_agent.TravelPlannerAgent._initialize_llms'):
            
            # 模拟HTTP响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock()
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # 模拟LLM响应
            mock_llm_response_intent = AsyncMock()
            mock_llm_response_intent.generations = [[AsyncMock(text=mock_llm_intent_response)]]
            
            mock_llm_response_planning = AsyncMock()
            mock_llm_response_planning.generations = [[AsyncMock(text=mock_llm_planning_response)]]
            
            with patch('src.core.llm_manager.LLMManager.get_llm') as mock_get_llm:
                mock_llm = AsyncMock()
                mock_llm.agenerate.side_effect = [mock_llm_response_intent, mock_llm_response_planning]
                mock_get_llm.return_value = mock_llm
                
                # 模拟工具调用
                with patch('src.tools.amap_tools.AmapAPIClient._make_request') as mock_amap_request:
                    mock_amap_request.side_effect = [
                        {"geocodes": [{"location": "116.397477,39.903738"}]},  # geocode
                        mock_amap_response,  # search_poi for 历史古迹
                        mock_amap_response,  # search_poi for 美食
                        mock_weather_response  # weather_info
                    ]
                    
                    # 发送规划请求
                    response = await async_client.post("/travel-planner/plan", json=planning_request)
                    
                    assert response.status_code == 200
                    result = response.json()
                    assert result["success"] is True
                    assert "trace_id" in result
                    assert "data" in result
                    
                    # 验证规划结果
                    itinerary_data = result["data"]["itinerary"]
                    assert itinerary_data["user_id"] == "integration_test_user"
                    assert itinerary_data["summary"]["title"] == "北京3日历史文化美食之旅"
                    assert itinerary_data["summary"]["duration_days"] == 3
                    assert len(itinerary_data["daily_plans"]) >= 1
                    
                    # 验证POI信息
                    day1_pois = itinerary_data["daily_plans"][0]["pois"]
                    assert len(day1_pois) >= 1
                    assert any(poi["name"] == "故宫博物院" for poi in day1_pois)
                    
                    trace_id = result["trace_id"]
        
        # 3. 验证数据持久化
        await asyncio.sleep(1)  # 等待后台任务完成
        
        # 检查行程是否保存到数据库
        saved_itinerary = await memory_manager.get_itinerary_by_trace_id(trace_id)
        assert saved_itinerary is not None
        assert saved_itinerary["user_id"] == "integration_test_user"
        
        # 4. 获取用户历史记录
        history_response = await async_client.get("/travel-planner/history/integration_test_user")
        assert history_response.status_code == 200
        history_data = history_response.json()
        assert history_data["success"] is True
        assert history_data["count"] >= 1
        
        # 5. 获取用户统计信息
        stats_response = await async_client.get("/travel-planner/user/integration_test_user/statistics")
        assert stats_response.status_code == 200
        stats_data = stats_response.json()
        assert stats_data["success"] is True
        assert stats_data["statistics"]["total_itineraries"] >= 1
    
    @pytest.mark.asyncio
    async def test_stream_planning_integration(self, async_client: AsyncClient):
        """测试流式规划集成"""
        # 启动流式规划
        stream_request = {
            "user_id": "stream_test_user",
            "query": "我想去上海玩2天，喜欢现代建筑和购物",
            "session_id": "test_stream_session",
            "context": {}
        }
        
        response = await async_client.post("/travel-planner/plan/stream", json=stream_request)
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        session_id = result["data"]["session_id"]
        
        # 测试SSE连接
        sse_response = await async_client.get(f"/travel-planner/plan/stream/{session_id}")
        assert sse_response.status_code == 200
        assert "text/event-stream" in sse_response.headers["content-type"]
    
    @pytest.mark.asyncio
    async def test_error_recovery_integration(self, async_client: AsyncClient):
        """测试错误恢复集成"""
        # 测试无效用户ID
        invalid_request = {
            "user_id": "",
            "query": "测试查询",
            "context": {}
        }
        
        response = await async_client.post("/travel-planner/plan", json=invalid_request)
        assert response.status_code == 422  # Validation error
        
        # 测试API错误处理
        error_request = {
            "user_id": "error_test_user",
            "query": "测试查询",
            "context": {}
        }
        
        with patch('src.agents.travel_planner_agent.TravelPlannerAgent.process', side_effect=Exception("Test error")):
            response = await async_client.post("/travel-planner/plan", json=error_request)
            assert response.status_code == 200
            result = response.json()
            assert result["success"] is False
            assert "error" in result
    
    @pytest.mark.asyncio
    async def test_tool_integration(self):
        """测试工具集成"""
        # 验证所有工具都已注册
        all_tools = tool_registry.list_tools()
        
        # 验证高德地图工具
        amap_tools = [
            "search_poi", "search_around", "geocode", "regeocode",
            "route_planning", "distance_matrix", "traffic_info",
            "weather_info", "administrative_district", "ip_location",
            "static_map", "inputtips", "bus_route", "walking_route",
            "cycling_route", "truck_route"
        ]
        
        for tool in amap_tools:
            assert tool in all_tools, f"Amap tool {tool} not registered"
        
        # 验证记忆工具
        memory_tools = [
            "get_user_profile", "save_user_profile", "update_user_preferences",
            "get_user_itineraries", "get_itinerary_by_trace_id",
            "save_interaction_log", "get_user_statistics", "analyze_user_behavior"
        ]
        
        for tool in memory_tools:
            assert tool in all_tools, f"Memory tool {tool} not registered"
        
        # 验证工具分类
        amap_category_tools = tool_registry.get_tools_by_category("amap")
        memory_category_tools = tool_registry.get_tools_by_category("memory")
        
        assert len(amap_category_tools) >= 15
        assert len(memory_category_tools) >= 8
    
    @pytest.mark.asyncio
    async def test_database_integration(self, setup_database):
        """测试数据库集成"""
        # 测试用户画像存储和检索
        from src.models.travel_models import User, UserProfile
        
        test_user = User(
            user_id="db_test_user",
            profile=UserProfile(
                name="数据库测试用户",
                preferred_budget="high",
                preferred_activities=["购物", "娱乐"],
                tags=["奢华游", "都市游"]
            )
        )
        
        # 保存用户
        save_success = await memory_manager.save_user_profile(test_user)
        assert save_success is True
        
        # 检索用户
        retrieved_user = await memory_manager.get_user_profile("db_test_user")
        assert retrieved_user is not None
        assert retrieved_user.user_id == "db_test_user"
        assert retrieved_user.profile.name == "数据库测试用户"
        assert "购物" in retrieved_user.profile.preferred_activities
        
        # 更新用户偏好
        update_success = await memory_manager.update_user_preferences(
            "db_test_user",
            {"preferred_budget": "medium", "tags": ["经济游"]}
        )
        assert update_success is True
        
        # 验证更新
        updated_user = await memory_manager.get_user_profile("db_test_user")
        assert updated_user.profile.preferred_budget == "medium"
        assert "经济游" in updated_user.profile.tags
    
    @pytest.mark.asyncio
    async def test_performance_integration(self, async_client: AsyncClient):
        """测试性能集成"""
        import time
        
        # 并发请求测试
        concurrent_requests = []
        start_time = time.time()
        
        for i in range(5):
            request_data = {
                "user_id": f"perf_test_user_{i}",
                "query": f"我想去城市{i}玩2天",
                "context": {}
            }
            
            # 模拟快速响应
            with patch('src.agents.travel_planner_agent.TravelPlannerAgent.process') as mock_process:
                mock_process.return_value = {
                    "status": "success",
                    "itinerary": {"trace_id": f"trace_{i}", "user_id": f"perf_test_user_{i}"},
                    "intent_analysis": {},
                    "tools_used": []
                }
                
                task = async_client.post("/travel-planner/plan", json=request_data)
                concurrent_requests.append(task)
        
        # 等待所有请求完成
        responses = await asyncio.gather(*concurrent_requests)
        end_time = time.time()
        
        # 验证所有请求都成功
        for response in responses:
            assert response.status_code == 200
            result = response.json()
            assert result["success"] is True
        
        # 验证响应时间合理（应该在10秒内完成5个并发请求）
        total_time = end_time - start_time
        assert total_time < 10, f"Concurrent requests took too long: {total_time}s"
    
    @pytest.mark.asyncio
    async def test_health_check_integration(self, async_client: AsyncClient, setup_database):
        """测试健康检查集成"""
        response = await async_client.get("/health")
        assert response.status_code == 200
        
        health_data = response.json()
        assert "status" in health_data
        assert "database" in health_data
        assert health_data["database"]["mongodb"] is True
