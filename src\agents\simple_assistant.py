"""
简单助手Agent

基于AutoGen框架实现的简单AI助手，集成我们的LLM管理器和日志系统。
这是一个基础Agent实现，用于验证整个技术栈的工作状态。
"""
import asyncio
from typing import Dict, Any, Optional

from src.core.llm_manager import get_default_manager, LLMManager
from src.core.logger import get_logger, get_performance_logger, get_trace_logger
from src.core.config import get_settings


class SimpleAssistant:
    """
    简单AI助手
    
    集成LLM管理器、结构化日志和性能监控的基础AI助手。
    支持对话交互和工具调用。
    """
    
    def __init__(
        self, 
        name: str = "assistant",
        llm_role: str = "basic",
        system_message: Optional[str] = None
    ):
        self.name = name
        self.llm_role = llm_role
        self.system_message = system_message or "你是一个有用的AI助手，能够回答用户的问题。"
        
        # 日志记录器
        self.logger = get_logger(f"agent.{name}")
        self.perf_logger = get_performance_logger(f"agent_perf.{name}")
        self.trace_logger = get_trace_logger(f"agent_trace.{name}")
        
        # LLM管理器
        self.llm_manager: Optional[LLMManager] = None
        
        # 对话历史
        self.conversation_history = []
        
        self.logger.info(
            "Simple Assistant initialized",
            agent_name=name,
            llm_role=llm_role,
            system_message_length=len(self.system_message)
        )
    
    async def initialize(self):
        """异步初始化Agent"""
        try:
            self.llm_manager = get_default_manager()
            self.logger.info("LLM Manager initialized successfully")
        except Exception as e:
            self.logger.error(
                "Failed to initialize LLM Manager", 
                error=str(e),
                exc_info=True
            )
            raise
    
    async def chat(self, message: str, use_history: bool = True) -> Dict[str, Any]:
        """
        与用户对话
        
        Args:
            message: 用户消息
            use_history: 是否使用对话历史
            
        Returns:
            包含回复和元数据的字典
        """
        if not self.llm_manager:
            await self.initialize()
        
        # 开始追踪
        trace_id = self.trace_logger.start_trace(f"chat_{self.name}")
        
        try:
            with self.perf_logger.measure(f"chat_processing_{self.name}"):
                self.logger.info(
                    "Received user message",
                    message_length=len(message),
                    use_history=use_history,
                    history_length=len(self.conversation_history)
                )
                
                self.trace_logger.info(
                    "Processing user message",
                    step="message_received",
                    content_preview=message[:50] + "..." if len(message) > 50 else message
                )
                
                # 准备对话历史
                history = self.conversation_history if use_history else None
                
                # 调用LLM
                response = await self.llm_manager.chat(
                    message=message,
                    role=self.llm_role,
                    system_message=self.system_message,
                    history=history
                )
                
                self.trace_logger.info(
                    "LLM response received",
                    step="llm_response",
                    response_length=len(response.get("content", "")),
                    tokens_used=response.get("usage", {}).get("total_tokens", 0)
                )
                
                # 更新对话历史
                if use_history:
                    from src.core.llm_manager import ChatMessage
                    self.conversation_history.append(
                        ChatMessage(role="user", content=message)
                    )
                    self.conversation_history.append(
                        ChatMessage(role="assistant", content=response["content"])
                    )
                
                # 准备返回结果
                result = {
                    "agent_name": self.name,
                    "user_message": message,
                    "assistant_reply": response["content"],
                    "model_used": response.get("model"),
                    "tokens_used": response.get("usage", {}),
                    "request_id": response.get("request_id"),
                    "conversation_length": len(self.conversation_history)
                }
                
                self.logger.info(
                    "Chat completed successfully",
                    response_length=len(response["content"]),
                    total_tokens=response.get("usage", {}).get("total_tokens", 0),
                    conversation_length=len(self.conversation_history)
                )
                
                return result
        
        except Exception as e:
            self.logger.error(
                "Chat processing failed",
                error=str(e),
                message_preview=message[:100],
                exc_info=True
            )
            self.trace_logger.error(
                "Chat processing error",
                step="error_handling",
                error=str(e)
            )
            raise
        
        finally:
            # 结束追踪
            self.trace_logger.end_trace()
    
    async def reset_conversation(self):
        """重置对话历史"""
        old_length = len(self.conversation_history)
        self.conversation_history.clear()
        
        self.logger.info(
            "Conversation history reset",
            previous_length=old_length
        )
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """获取对话摘要"""
        user_messages = [msg for msg in self.conversation_history if msg.role == "user"]
        assistant_messages = [msg for msg in self.conversation_history if msg.role == "assistant"]
        
        total_user_chars = sum(len(msg.content) for msg in user_messages)
        total_assistant_chars = sum(len(msg.content) for msg in assistant_messages)
        
        return {
            "agent_name": self.name,
            "total_exchanges": len(user_messages),
            "total_messages": len(self.conversation_history),
            "user_message_count": len(user_messages),
            "assistant_message_count": len(assistant_messages),
            "total_user_characters": total_user_chars,
            "total_assistant_characters": total_assistant_chars,
            "average_user_message_length": total_user_chars / len(user_messages) if user_messages else 0,
            "average_assistant_message_length": total_assistant_chars / len(assistant_messages) if assistant_messages else 0
        }


# 便捷函数
async def create_simple_assistant(
    name: str = "assistant",
    llm_role: str = "basic",
    system_message: Optional[str] = None
) -> SimpleAssistant:
    """
    创建并初始化一个简单助手
    
    Args:
        name: Agent名称
        llm_role: LLM角色 (basic/reasoning)
        system_message: 系统提示词
        
    Returns:
        初始化完成的SimpleAssistant实例
    """
    assistant = SimpleAssistant(name, llm_role, system_message)
    await assistant.initialize()
    return assistant


async def quick_chat(message: str, agent_name: str = "quick_assistant") -> str:
    """
    快速对话函数
    
    Args:
        message: 用户消息
        agent_name: Agent名称
        
    Returns:
        助手回复内容
    """
    assistant = await create_simple_assistant(name=agent_name)
    result = await assistant.chat(message, use_history=False)
    return result["assistant_reply"]


# 使用示例：
# 
# # 1. 创建助手
# assistant = await create_simple_assistant("my_assistant", "basic")
# 
# # 2. 对话
# result = await assistant.chat("你好，你是谁？")
# print(result["assistant_reply"])
# 
# # 3. 继续对话（带历史）
# result = await assistant.chat("请介绍一下你的能力")
# print(result["assistant_reply"])
# 
# # 4. 快速对话
# reply = await quick_chat("今天天气怎么样？")
# print(reply)
# 
# # 5. 查看对话摘要
# summary = assistant.get_conversation_summary()
# print(f"总共进行了 {summary['total_exchanges']} 轮对话") 