"""
Python编程专家Agent

一个专门的垂域Agent，专注于Python编程相关的问题解答、代码审查、调试和最佳实践指导。
这个Agent展示了如何创建专业领域的AI助手。
"""
import asyncio
import re
import ast
import inspect
from typing import Dict, Any, List, Optional, Callable
from pathlib import Path

from src.agents.simple_assistant import SimpleAssistant
from src.core.logger import get_logger, get_performance_logger, get_trace_logger


class PythonExpert(SimpleAssistant):
    """
    Python编程专家Agent
    
    专业领域功能：
    - Python代码分析和优化建议
    - 代码质量评估
    - 最佳实践建议
    - 常见问题诊断
    - 性能优化建议
    - 库推荐和使用指导
    """
    
    def __init__(self, name: str = "python_expert"):
        # 专业的系统提示词
        system_message = """你是一位资深的Python编程专家，拥有超过15年的Python开发经验。你精通：

🐍 **核心专业领域：**
- Python语言特性和最佳实践
- 异步编程(asyncio)和并发编程
- 面向对象设计模式和函数式编程
- 性能优化和内存管理
- 测试驱动开发(TDD)和代码质量
- Web开发(FastAPI, Django, Flask)
- 数据科学(pandas, numpy, scikit-learn)
- DevOps和部署(Docker, CI/CD)

📋 **工作方式：**
1. 提供准确、可执行的代码示例
2. 解释代码背后的原理和最佳实践
3. 指出潜在问题和改进建议
4. 推荐合适的库和工具
5. 考虑性能、可维护性和安全性

🎯 **回答特点：**
- 代码示例必须可直接运行
- 包含详细的注释和说明
- 提供多种解决方案供选择
- 强调代码质量和最佳实践
- 适当时提供单元测试示例

请用专业但友好的语气回答用户的Python相关问题。"""
        
        super().__init__(
            name=name,
            llm_role="reasoning",  # 使用思考模型获得更好的代码分析能力
            system_message=system_message
        )
        
        # 专业工具
        self.code_patterns = self._load_code_patterns()
        self.python_tools = self._initialize_tools()
        
        self.logger.info(
            "Python Expert Agent initialized",
            tools_count=len(self.python_tools),
            patterns_count=len(self.code_patterns)
        )
    
    def _load_code_patterns(self) -> Dict[str, str]:
        """加载Python代码模式和反模式"""
        return {
            "list_comprehension": r"\[.*for.*in.*\]",
            "generator_expression": r"\(.*for.*in.*\)",
            "dict_comprehension": r"\{.*:.*for.*in.*\}",
            "lambda_function": r"lambda\s+.*:",
            "decorator_pattern": r"@\w+.*\ndef",
            "context_manager": r"with\s+.*:",
            "async_function": r"async\s+def\s+\w+",
            "await_expression": r"await\s+\w+",
            "type_hints": r":\s*\w+.*->",
            "dataclass": r"@dataclass",
            "property_decorator": r"@property",
            "staticmethod": r"@staticmethod",
            "classmethod": r"@classmethod"
        }
    
    def _initialize_tools(self) -> Dict[str, Callable]:
        """初始化Python专业工具"""
        return {
            "analyze_code": self.analyze_code,
            "suggest_optimization": self.suggest_optimization,
            "check_best_practices": self.check_best_practices,
            "recommend_libraries": self.recommend_libraries,
            "generate_test": self.generate_test,
            "explain_error": self.explain_error,
            "performance_tips": self.performance_tips
        }
    
    async def analyze_code(self, code: str) -> Dict[str, Any]:
        """
        分析Python代码
        
        Args:
            code: Python代码字符串
            
        Returns:
            代码分析结果
        """
        trace_id = self.trace_logger.start_trace("code_analysis")
        
        try:
            with self.perf_logger.measure("code_analysis"):
                analysis = {
                    "syntax_valid": False,
                    "complexity": 0,
                    "patterns_found": [],
                    "issues": [],
                    "suggestions": []
                }
                
                # 语法检查
                try:
                    ast.parse(code)
                    analysis["syntax_valid"] = True
                    self.trace_logger.info("Syntax validation passed", step="syntax_check")
                except SyntaxError as e:
                    analysis["issues"].append(f"语法错误: {e}")
                    self.trace_logger.error("Syntax error found", step="syntax_check", error=str(e))
                
                # 模式识别
                for pattern_name, pattern in self.code_patterns.items():
                    if re.search(pattern, code):
                        analysis["patterns_found"].append(pattern_name)
                
                # 复杂度估算（简单的行数和嵌套层级）
                lines = code.split('\n')
                analysis["complexity"] = len([l for l in lines if l.strip() and not l.strip().startswith('#')])
                
                # 常见问题检查
                issues = []
                if "global " in code:
                    issues.append("使用了global变量，建议考虑更好的设计模式")
                if "import *" in code:
                    issues.append("使用了import *，建议显式导入")
                if re.search(r"except:", code):
                    issues.append("使用了裸except，建议指定具体异常类型")
                
                analysis["issues"] = issues
                
                self.logger.info(
                    "Code analysis completed",
                    syntax_valid=analysis["syntax_valid"],
                    patterns_count=len(analysis["patterns_found"]),
                    issues_count=len(analysis["issues"]),
                    complexity=analysis["complexity"]
                )
                
                return analysis
        
        except Exception as e:
            self.logger.error("Code analysis failed", error=str(e), exc_info=True)
            raise
        finally:
            self.trace_logger.end_trace()
    
    async def suggest_optimization(self, code: str) -> List[str]:
        """代码优化建议"""
        suggestions = []
        
        # 检查常见优化点
        if "for i in range(len(" in code:
            suggestions.append("使用enumerate()替代range(len())来遍历索引和值")
        
        if re.search(r"if\s+.*==\s*True", code):
            suggestions.append("直接使用if condition而不是if condition == True")
        
        if re.search(r"if\s+len\(.*\)\s*[><=]", code):
            suggestions.append("对于检查容器是否为空，使用if container而不是if len(container) > 0")
        
        return suggestions
    
    async def check_best_practices(self, code: str) -> Dict[str, List[str]]:
        """检查最佳实践"""
        practices = {
            "good": [],
            "needs_improvement": []
        }
        
        # 好的实践
        if "def " in code and ":" in code:
            if re.search(r'""".*"""', code, re.DOTALL):
                practices["good"].append("使用了docstring文档")
        
        if "typing" in code or "->" in code:
            practices["good"].append("使用了类型提示")
        
        if "@dataclass" in code:
            practices["good"].append("使用了dataclass简化类定义")
        
        # 需要改进
        if re.search(r"except\s*:", code):
            practices["needs_improvement"].append("应该捕获具体的异常类型")
        
        if "TODO" in code.upper() or "FIXME" in code.upper():
            practices["needs_improvement"].append("代码中有待办事项，建议完善")
        
        return practices
    
    async def recommend_libraries(self, task_description: str) -> List[Dict[str, str]]:
        """基于任务推荐库"""
        recommendations = []
        
        task_lower = task_description.lower()
        
        # Web开发
        if any(word in task_lower for word in ["web", "api", "http", "服务器"]):
            recommendations.extend([
                {"name": "FastAPI", "purpose": "现代、快速的Web框架，自动生成API文档"},
                {"name": "requests", "purpose": "优雅的HTTP库"},
                {"name": "httpx", "purpose": "支持异步的HTTP客户端"}
            ])
        
        # 数据处理
        if any(word in task_lower for word in ["数据", "data", "分析", "csv", "excel"]):
            recommendations.extend([
                {"name": "pandas", "purpose": "强大的数据分析和操作库"},
                {"name": "numpy", "purpose": "科学计算基础库"},
                {"name": "openpyxl", "purpose": "Excel文件读写"}
            ])
        
        # 机器学习
        if any(word in task_lower for word in ["机器学习", "ml", "ai", "模型", "训练"]):
            recommendations.extend([
                {"name": "scikit-learn", "purpose": "机器学习算法库"},
                {"name": "torch", "purpose": "深度学习框架"},
                {"name": "transformers", "purpose": "预训练模型库"}
            ])
        
        # 异步编程
        if any(word in task_lower for word in ["异步", "async", "并发", "协程"]):
            recommendations.extend([
                {"name": "asyncio", "purpose": "Python内置异步编程库"},
                {"name": "aiohttp", "purpose": "异步HTTP客户端/服务器"},
                {"name": "aiofiles", "purpose": "异步文件操作"}
            ])
        
        return recommendations
    
    async def generate_test(self, function_code: str) -> str:
        """为函数生成单元测试"""
        # 提取函数名
        match = re.search(r"def\s+(\w+)\s*\(", function_code)
        if not match:
            return "# 无法识别函数定义"
        
        func_name = match.group(1)
        
        test_template = f'''import unittest
from unittest.mock import patch, MagicMock

class Test{func_name.title()}(unittest.TestCase):
    
    def test_{func_name}_basic(self):
        """测试基本功能"""
        # TODO: 添加测试用例
        pass
    
    def test_{func_name}_edge_cases(self):
        """测试边界情况"""
        # TODO: 添加边界测试
        pass
    
    def test_{func_name}_error_handling(self):
        """测试错误处理"""
        # TODO: 添加异常测试
        with self.assertRaises(Exception):
            pass

if __name__ == '__main__':
    unittest.main()'''
        
        return test_template
    
    async def explain_error(self, error_message: str) -> Dict[str, str]:
        """解释Python错误信息"""
        explanations = {
            "NameError": "变量或函数名未定义，检查拼写和作用域",
            "TypeError": "类型错误，检查变量类型和函数参数",
            "IndexError": "索引超出范围，检查列表/字符串长度",
            "KeyError": "字典键不存在，使用get()方法或检查键是否存在",
            "AttributeError": "对象没有该属性或方法，检查对象类型",
            "ImportError": "模块导入失败，检查模块名和安装状态",
            "SyntaxError": "语法错误，检查括号、引号和缩进",
            "IndentationError": "缩进错误，统一使用空格或制表符"
        }
        
        for error_type, explanation in explanations.items():
            if error_type in error_message:
                return {
                    "error_type": error_type,
                    "explanation": explanation,
                    "suggestion": f"常见{error_type}解决方法：{explanation}"
                }
        
        return {
            "error_type": "Unknown",
            "explanation": "未识别的错误类型",
            "suggestion": "请提供完整的错误堆栈信息以获得更准确的帮助"
        }
    
    async def performance_tips(self, code_type: str) -> List[str]:
        """性能优化建议"""
        tips_map = {
            "loop": [
                "使用列表推导式替代简单循环",
                "考虑使用map()和filter()进行函数式编程",
                "避免在循环中重复计算不变的值",
                "使用enumerate()而不是range(len())"
            ],
            "data": [
                "使用pandas进行大数据操作",
                "考虑使用numpy数组替代Python列表",
                "使用生成器表达式节省内存",
                "批量操作替代逐个操作"
            ],
            "io": [
                "使用异步I/O处理大量网络请求",
                "批量读写文件减少系统调用",
                "使用缓存减少重复计算",
                "考虑使用多进程处理CPU密集型任务"
            ],
            "general": [
                "使用内置函数和库函数",
                "避免过早优化，先保证正确性",
                "使用性能分析工具(cProfile)定位瓶颈",
                "考虑算法复杂度优化"
            ]
        }
        
        return tips_map.get(code_type, tips_map["general"])
    
    async def python_chat(self, message: str, code: str = None) -> Dict[str, Any]:
        """
        Python专家对话接口
        
        Args:
            message: 用户问题
            code: 可选的代码片段
            
        Returns:
            专家回复和分析结果
        """
        trace_id = self.trace_logger.start_trace(f"python_expert_chat")
        
        try:
            # 准备上下文信息
            context_info = []
            
            if code:
                # 分析提供的代码
                analysis = await self.analyze_code(code)
                context_info.append(f"代码分析结果：{analysis}")
                
                # 检查最佳实践
                practices = await self.check_best_practices(code)
                context_info.append(f"最佳实践检查：{practices}")
            
            # 构建增强的问题
            enhanced_message = message
            if context_info:
                enhanced_message += f"\n\n附加分析信息：\n" + "\n".join(context_info)
            
            # 调用基础对话功能
            result = await self.chat(enhanced_message, use_history=True)
            
            # 添加专家分析
            expert_analysis = {
                "code_analysis": analysis if code else None,
                "recommendations": await self.recommend_libraries(message) if any(word in message.lower() for word in ["库", "library", "包", "package"]) else [],
                "performance_tips": await self.performance_tips("general")
            }
            
            result["expert_analysis"] = expert_analysis
            
            self.logger.info(
                "Python expert chat completed",
                has_code=code is not None,
                recommendations_count=len(expert_analysis["recommendations"]),
                performance_tips_count=len(expert_analysis["performance_tips"])
            )
            
            return result
        
        except Exception as e:
            self.logger.error("Python expert chat failed", error=str(e), exc_info=True)
            raise
        finally:
            self.trace_logger.end_trace()


# 便捷函数
async def create_python_expert(name: str = "python_expert") -> PythonExpert:
    """创建Python专家"""
    expert = PythonExpert(name)
    await expert.initialize()
    return expert


async def quick_python_help(question: str, code: str = None) -> str:
    """快速Python问题咨询"""
    expert = await create_python_expert("quick_python_expert")
    result = await expert.python_chat(question, code)
    return result["assistant_reply"]


# 使用示例：
#
# # 1. 创建Python专家
# expert = await create_python_expert("my_python_expert")
#
# # 2. 普通Python问题
# result = await expert.python_chat("如何实现单例模式？")
# print(result["assistant_reply"])
#
# # 3. 代码审查
# code = '''
# def fibonacci(n):
#     if n <= 1:
#         return n
#     return fibonacci(n-1) + fibonacci(n-2)
# '''
# result = await expert.python_chat("请帮我优化这个斐波那契函数", code)
# print(result["assistant_reply"])
# print("专家分析:", result["expert_analysis"])
#
# # 4. 快速帮助
# answer = await quick_python_help("Python中如何处理异常？")
# print(answer) 