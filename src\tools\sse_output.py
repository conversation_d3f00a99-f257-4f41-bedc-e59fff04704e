"""
SSE (Server-Sent Events) 输出管理器

提供实时流式输出功能，支持思考过程和执行进度的实时反馈。
"""
import json
import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime
from enum import Enum
from pydantic import BaseModel
from src.core.logger import get_logger

logger = get_logger('tools.sse_output')


class SSEEventType(str, Enum):
    """SSE事件类型"""
    THINKING = "thinking"
    TOOL_CALL = "tool_call"
    TOOL_RESULT = "tool_result"
    PROGRESS = "progress"
    ERROR = "error"
    COMPLETE = "complete"
    HEARTBEAT = "heartbeat"


class SSEMessage(BaseModel):
    """SSE消息模型"""
    event_type: SSEEventType
    data: Dict[str, Any]
    timestamp: datetime = None
    trace_id: Optional[str] = None
    
    def __init__(self, **data):
        if 'timestamp' not in data:
            data['timestamp'] = datetime.now()
        super().__init__(**data)


class SSEOutputManager:
    """SSE输出管理器"""
    
    def __init__(self):
        self._clients: Dict[str, asyncio.Queue] = {}
        self._active_sessions: Dict[str, bool] = {}
    
    def register_client(self, session_id: str) -> asyncio.Queue:
        """注册客户端"""
        queue = asyncio.Queue()
        self._clients[session_id] = queue
        self._active_sessions[session_id] = True
        logger.info(f"Registered SSE client: {session_id}")
        return queue
    
    def unregister_client(self, session_id: str):
        """注销客户端"""
        if session_id in self._clients:
            self._active_sessions[session_id] = False
            del self._clients[session_id]
            logger.info(f"Unregistered SSE client: {session_id}")
    
    async def send_message(self, session_id: str, message: SSEMessage):
        """发送消息到指定客户端"""
        if session_id in self._clients and self._active_sessions.get(session_id, False):
            try:
                await self._clients[session_id].put(message)
                logger.debug(f"Sent SSE message to {session_id}: {message.event_type}")
            except Exception as e:
                logger.error(f"Failed to send SSE message to {session_id}: {str(e)}")
    
    async def broadcast_message(self, message: SSEMessage):
        """广播消息到所有客户端"""
        for session_id in list(self._clients.keys()):
            await self.send_message(session_id, message)
    
    async def send_thinking(self, session_id: str, content: str, trace_id: Optional[str] = None):
        """发送思考过程消息"""
        message = SSEMessage(
            event_type=SSEEventType.THINKING,
            data={"content": content},
            trace_id=trace_id
        )
        await self.send_message(session_id, message)
    
    async def send_tool_call(self, session_id: str, tool_name: str, parameters: Dict[str, Any], trace_id: Optional[str] = None):
        """发送工具调用消息"""
        message = SSEMessage(
            event_type=SSEEventType.TOOL_CALL,
            data={
                "tool_name": tool_name,
                "parameters": parameters
            },
            trace_id=trace_id
        )
        await self.send_message(session_id, message)
    
    async def send_tool_result(self, session_id: str, tool_name: str, result: Any, trace_id: Optional[str] = None):
        """发送工具结果消息"""
        message = SSEMessage(
            event_type=SSEEventType.TOOL_RESULT,
            data={
                "tool_name": tool_name,
                "result": result
            },
            trace_id=trace_id
        )
        await self.send_message(session_id, message)
    
    async def send_progress(self, session_id: str, step: str, progress: float, trace_id: Optional[str] = None):
        """发送进度消息"""
        message = SSEMessage(
            event_type=SSEEventType.PROGRESS,
            data={
                "step": step,
                "progress": progress
            },
            trace_id=trace_id
        )
        await self.send_message(session_id, message)
    
    async def send_error(self, session_id: str, error: str, trace_id: Optional[str] = None):
        """发送错误消息"""
        message = SSEMessage(
            event_type=SSEEventType.ERROR,
            data={"error": error},
            trace_id=trace_id
        )
        await self.send_message(session_id, message)
    
    async def send_complete(self, session_id: str, result: Any, trace_id: Optional[str] = None):
        """发送完成消息"""
        message = SSEMessage(
            event_type=SSEEventType.COMPLETE,
            data={"result": result},
            trace_id=trace_id
        )
        await self.send_message(session_id, message)
    
    async def stream_events(self, session_id: str) -> AsyncGenerator[str, None]:
        """生成SSE事件流"""
        queue = self.register_client(session_id)
        
        try:
            # 发送连接确认
            yield self._format_sse_message("connected", {"session_id": session_id})
            
            # 定期发送心跳
            heartbeat_task = asyncio.create_task(self._send_heartbeat(session_id))
            
            while self._active_sessions.get(session_id, False):
                try:
                    # 等待消息，设置超时避免阻塞
                    message = await asyncio.wait_for(queue.get(), timeout=1.0)
                    yield self._format_sse_message(message.event_type.value, message.dict())
                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    logger.error(f"Error in SSE stream for {session_id}: {str(e)}")
                    break
        
        finally:
            heartbeat_task.cancel()
            self.unregister_client(session_id)
    
    async def _send_heartbeat(self, session_id: str):
        """定期发送心跳"""
        while self._active_sessions.get(session_id, False):
            try:
                await asyncio.sleep(30)  # 每30秒发送一次心跳
                message = SSEMessage(
                    event_type=SSEEventType.HEARTBEAT,
                    data={"timestamp": datetime.now().isoformat()}
                )
                await self.send_message(session_id, message)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Heartbeat error for {session_id}: {str(e)}")
    
    def _format_sse_message(self, event: str, data: Dict[str, Any]) -> str:
        """格式化SSE消息"""
        return f"event: {event}\ndata: {json.dumps(data, default=str, ensure_ascii=False)}\n\n"


# 全局SSE输出管理器实例
sse_manager = SSEOutputManager()
