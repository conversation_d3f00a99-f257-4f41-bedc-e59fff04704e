/* 智能旅行规划助手样式文件 */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    margin-bottom: 20px;
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* 功能特性展示 */
.feature-item {
    text-align: center;
    padding: 20px;
}

.feature-item i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.feature-item h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* 思考过程样式 */
.thinking-content {
    max-height: 300px;
    overflow-y: auto;
    font-size: 0.9rem;
}

.thinking-item {
    padding: 8px 12px;
    margin-bottom: 8px;
    background-color: var(--light-color);
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 行程概要样式 */
.trip-summary {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.trip-summary h4 {
    margin-bottom: 10px;
    font-weight: bold;
}

.trip-summary .summary-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 15px;
}

.trip-summary .stat-item {
    text-align: center;
}

.trip-summary .stat-item i {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

/* 每日行程样式 */
.day-plan {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    margin-bottom: 20px;
    overflow: hidden;
}

.day-header {
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    color: white;
    padding: 15px 20px;
    font-weight: bold;
}

.day-content {
    padding: 20px;
}

.poi-item {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    margin-bottom: 15px;
    background-color: var(--light-color);
    border-radius: 8px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.poi-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.poi-icon {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.poi-details h6 {
    margin-bottom: 5px;
    color: var(--dark-color);
    font-weight: 600;
}

.poi-details .poi-address {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.poi-details .poi-description {
    font-size: 0.9rem;
    color: var(--dark-color);
}

.poi-tags {
    margin-top: 8px;
}

.poi-tag {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-right: 5px;
    margin-bottom: 3px;
}

/* 地图链接样式 */
.map-links {
    background-color: var(--light-color);
    padding: 20px;
    border-radius: 10px;
}

.map-link {
    display: inline-block;
    margin: 5px;
    padding: 8px 15px;
    background-color: var(--success-color);
    color: white;
    text-decoration: none;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
}

.map-link:hover {
    background-color: #157347;
    color: white;
    text-decoration: none;
}

/* 历史记录样式 */
.history-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.history-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
}

.history-item h6 {
    margin-bottom: 8px;
    color: var(--dark-color);
}

.history-item .history-meta {
    font-size: 0.85rem;
    color: var(--secondary-color);
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .trip-summary .summary-stats {
        flex-direction: column;
        gap: 10px;
    }
    
    .poi-item {
        flex-direction: column;
        text-align: center;
    }
    
    .poi-icon {
        margin: 0 auto 10px auto;
    }
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: var(--dark-color);
    color: white;
    border-radius: 6px;
}

/* 按钮样式增强 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 表单样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color);
}
