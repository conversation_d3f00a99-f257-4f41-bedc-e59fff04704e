"""
工具模块测试用例
"""
import pytest
from unittest.mock import patch, AsyncMock
from src.tools.registry import tool_registry
from src.tools.amap_tools import AmapAPIClient
from src.tools.memory_tools import get_user_profile, save_user_profile


class TestToolRegistry:
    """工具注册器测试类"""
    
    def test_tool_registration(self):
        """测试工具注册"""
        # 注册一个测试工具
        @tool_registry.register(
            name="test_tool",
            description="测试工具",
            category="test"
        )
        async def test_tool_func(param1: str, param2: int = 10):
            return {"param1": param1, "param2": param2}
        
        # 验证工具已注册
        assert "test_tool" in tool_registry.list_tools()
        
        # 验证工具信息
        tool_info = tool_registry.get_tool_info("test_tool")
        assert tool_info["name"] == "test_tool"
        assert tool_info["description"] == "测试工具"
        assert tool_info["category"] == "test"
        
        # 验证参数模式
        schema = tool_info["parameters_schema"]
        assert "param1" in schema["properties"]
        assert "param2" in schema["properties"]
        assert "param1" in schema["required"]
        assert "param2" not in schema["required"]  # 有默认值
    
    @pytest.mark.asyncio
    async def test_tool_execution(self):
        """测试工具执行"""
        @tool_registry.register(name="test_exec_tool", category="test")
        async def test_exec_tool(message: str):
            return f"Processed: {message}"
        
        result = await tool_registry.execute_tool("test_exec_tool", message="Hello")
        assert result == "Processed: Hello"
    
    @pytest.mark.asyncio
    async def test_tool_execution_error(self):
        """测试工具执行错误"""
        @tool_registry.register(name="test_error_tool", category="test")
        async def test_error_tool():
            raise ValueError("Test error")
        
        with pytest.raises(ValueError, match="Test error"):
            await tool_registry.execute_tool("test_error_tool")
    
    def test_get_tools_by_category(self):
        """测试按分类获取工具"""
        # 注册不同分类的工具
        @tool_registry.register(name="amap_tool_1", category="amap")
        async def amap_tool_1():
            pass
        
        @tool_registry.register(name="memory_tool_1", category="memory")
        async def memory_tool_1():
            pass
        
        amap_tools = tool_registry.get_tools_by_category("amap")
        memory_tools = tool_registry.get_tools_by_category("memory")
        
        assert "amap_tool_1" in amap_tools
        assert "memory_tool_1" in memory_tools
        assert "amap_tool_1" not in memory_tools
        assert "memory_tool_1" not in amap_tools


class TestAmapTools:
    """高德地图工具测试类"""
    
    @pytest.mark.asyncio
    async def test_amap_client_context_manager(self):
        """测试高德地图客户端上下文管理器"""
        async with AmapAPIClient() as client:
            assert client.session is not None
        # 上下文退出后session应该被关闭
    
    @pytest.mark.asyncio
    async def test_search_poi_success(self, mock_amap_response):
        """测试POI搜索成功"""
        with patch('src.tools.amap_tools.AmapAPIClient._make_request', return_value=mock_amap_response):
            # 导入工具函数
            from src.tools.amap_tools import search_poi
            
            result = await search_poi(
                keywords="故宫",
                city="北京",
                offset=10
            )
            
            assert result == mock_amap_response
    
    @pytest.mark.asyncio
    async def test_search_around_success(self, mock_amap_response):
        """测试周边搜索成功"""
        with patch('src.tools.amap_tools.AmapAPIClient._make_request', return_value=mock_amap_response):
            from src.tools.amap_tools import search_around
            
            result = await search_around(
                location="116.397477,39.903738",
                keywords="餐厅",
                radius=1000
            )
            
            assert result == mock_amap_response
    
    @pytest.mark.asyncio
    async def test_geocode_success(self):
        """测试地理编码成功"""
        mock_response = {
            "status": "1",
            "geocodes": [
                {
                    "formatted_address": "北京市东城区景山前街4号",
                    "location": "116.397026,39.918058"
                }
            ]
        }
        
        with patch('src.tools.amap_tools.AmapAPIClient._make_request', return_value=mock_response):
            from src.tools.amap_tools import geocode
            
            result = await geocode(address="故宫博物院", city="北京")
            
            assert result == mock_response
    
    @pytest.mark.asyncio
    async def test_weather_info_success(self, mock_weather_response):
        """测试天气信息获取成功"""
        with patch('src.tools.amap_tools.AmapAPIClient._make_request', return_value=mock_weather_response):
            from src.tools.amap_tools import weather_info
            
            result = await weather_info(city="北京")
            
            assert result == mock_weather_response
    
    @pytest.mark.asyncio
    async def test_route_planning_success(self):
        """测试路径规划成功"""
        mock_response = {
            "status": "1",
            "route": {
                "paths": [
                    {
                        "distance": "5432",
                        "duration": "1234",
                        "steps": []
                    }
                ]
            }
        }
        
        with patch('src.tools.amap_tools.AmapAPIClient._make_request', return_value=mock_response):
            from src.tools.amap_tools import route_planning
            
            result = await route_planning(
                origin="116.397477,39.903738",
                destination="116.397026,39.918058"
            )
            
            assert result == mock_response
    
    @pytest.mark.asyncio
    async def test_amap_api_error_handling(self):
        """测试高德地图API错误处理"""
        with patch('src.tools.amap_tools.AmapAPIClient._make_request', side_effect=Exception("API Error")):
            from src.tools.amap_tools import search_poi
            
            with pytest.raises(Exception, match="API Error"):
                await search_poi(keywords="测试")


class TestMemoryTools:
    """记忆工具测试类"""
    
    @pytest.mark.asyncio
    async def test_get_user_profile_success(self, sample_user):
        """测试获取用户画像成功"""
        with patch('src.tools.memory_tools.memory_manager') as mock_memory:
            mock_memory.get_user_profile.return_value = sample_user
            
            result = await get_user_profile(user_id="test_user_001")
            
            assert result["success"] is True
            assert result["user_profile"]["user_id"] == "test_user_001"
            assert result["user_profile"]["profile"]["name"] == "测试用户"
    
    @pytest.mark.asyncio
    async def test_get_user_profile_not_found(self):
        """测试获取不存在的用户画像"""
        with patch('src.tools.memory_tools.memory_manager') as mock_memory:
            mock_memory.get_user_profile.return_value = None
            
            result = await get_user_profile(user_id="nonexistent_user")
            
            assert result["success"] is False
            assert result["user_profile"] is None
    
    @pytest.mark.asyncio
    async def test_save_user_profile_success(self):
        """测试保存用户画像成功"""
        with patch('src.tools.memory_tools.memory_manager') as mock_memory:
            mock_memory.save_user_profile.return_value = True
            
            result = await save_user_profile(
                user_id="test_user_001",
                name="新用户",
                preferred_budget="high",
                preferred_activities=["购物", "美食"],
                tags=["奢华游"]
            )
            
            assert result["success"] is True
            mock_memory.save_user_profile.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_save_user_profile_failure(self):
        """测试保存用户画像失败"""
        with patch('src.tools.memory_tools.memory_manager') as mock_memory:
            mock_memory.save_user_profile.return_value = False
            
            result = await save_user_profile(
                user_id="test_user_001",
                name="测试用户"
            )
            
            assert result["success"] is False
    
    @pytest.mark.asyncio
    async def test_get_user_itineraries_success(self):
        """测试获取用户行程记录成功"""
        mock_itineraries = [
            {
                "trace_id": "trace_001",
                "user_id": "test_user_001",
                "summary": {"title": "北京3日游"}
            }
        ]
        
        with patch('src.tools.memory_tools.memory_manager') as mock_memory:
            mock_memory.get_user_itineraries.return_value = mock_itineraries
            
            from src.tools.memory_tools import get_user_itineraries
            result = await get_user_itineraries(user_id="test_user_001", limit=10)
            
            assert result["success"] is True
            assert result["count"] == 1
            assert len(result["itineraries"]) == 1
    
    @pytest.mark.asyncio
    async def test_analyze_user_behavior(self, sample_user):
        """测试用户行为分析"""
        mock_stats = {
            "total_itineraries": 8,
            "recent_activity": 3,
            "popular_destinations": [
                {"destination": "北京", "count": 3}
            ]
        }
        
        with patch('src.tools.memory_tools.get_user_profile') as mock_get_profile, \
             patch('src.tools.memory_tools.get_user_itineraries') as mock_get_itineraries, \
             patch('src.tools.memory_tools.get_user_statistics') as mock_get_stats:
            
            mock_get_profile.return_value = {
                "success": True,
                "user_profile": sample_user.model_dump()
            }
            mock_get_itineraries.return_value = {
                "success": True,
                "itineraries": []
            }
            mock_get_stats.return_value = {
                "success": True,
                "statistics": mock_stats
            }
            
            from src.tools.memory_tools import analyze_user_behavior
            result = await analyze_user_behavior(user_id="test_user_001")
            
            assert result["success"] is True
            assert "analysis" in result
            assert "travel_frequency" in result["analysis"]
            assert "recommendations" in result["analysis"]
    
    @pytest.mark.asyncio
    async def test_memory_tool_error_handling(self):
        """测试记忆工具错误处理"""
        with patch('src.tools.memory_tools.memory_manager') as mock_memory:
            mock_memory.get_user_profile.side_effect = Exception("Database Error")
            
            result = await get_user_profile(user_id="test_user_001")
            
            assert result["success"] is False
            assert "error" in result
