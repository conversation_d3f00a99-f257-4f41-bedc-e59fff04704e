"""
记忆系统管理器

提供用户画像、行程记录、执行日志的存储和检索功能。
"""
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from motor.motor_asyncio import AsyncIOMotorDatabase
from src.core.database import get_mongodb
from src.models.travel_models import User, TravelItinerary, UserProfile
from src.core.logger import get_logger

logger = get_logger('memory.manager')


class MemoryManager:
    """记忆系统管理器"""
    
    def __init__(self):
        self._db: Optional[AsyncIOMotorDatabase] = None
    
    async def _get_db(self) -> AsyncIOMotorDatabase:
        """获取数据库实例"""
        if self._db is None:
            self._db = await get_mongodb()
        return self._db
    
    # ==================== 用户画像管理 ====================
    
    async def get_user_profile(self, user_id: str) -> Optional[User]:
        """获取用户画像"""
        try:
            db = await self._get_db()
            user_data = await db.users.find_one({"user_id": user_id})
            
            if user_data:
                # 转换MongoDB文档为User对象
                profile_data = user_data.get('profile', {})
                profile = UserProfile(
                    name=profile_data.get('name'),
                    avatar=profile_data.get('avatar'),
                    preferred_budget=profile_data.get('preferred_budget', 'medium'),
                    preferred_activities=profile_data.get('preferred_activities', []),
                    tags=user_data.get('tags', [])
                )
                
                user = User(
                    user_id=user_data['user_id'],
                    created_at=user_data.get('created_at', datetime.now()),
                    updated_at=user_data.get('updated_at', datetime.now()),
                    profile=profile
                )
                
                return user
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get user profile for {user_id}: {str(e)}")
            return None
    
    async def save_user_profile(self, user: User) -> bool:
        """保存用户画像"""
        try:
            db = await self._get_db()
            
            user_data = {
                "user_id": user.user_id,
                "created_at": user.created_at,
                "updated_at": datetime.now(),
                "profile": {
                    "name": user.profile.name,
                    "avatar": user.profile.avatar,
                    "preferred_budget": user.profile.preferred_budget,
                    "preferred_activities": user.profile.preferred_activities
                },
                "tags": user.profile.tags
            }
            
            # 使用upsert操作
            result = await db.users.update_one(
                {"user_id": user.user_id},
                {"$set": user_data},
                upsert=True
            )
            
            logger.info(f"User profile saved for {user.user_id}: {result.acknowledged}")
            return result.acknowledged
            
        except Exception as e:
            logger.error(f"Failed to save user profile for {user.user_id}: {str(e)}")
            return False
    
    async def update_user_preferences(self, user_id: str, preferences: Dict[str, Any]) -> bool:
        """更新用户偏好"""
        try:
            db = await self._get_db()
            
            update_data = {
                "updated_at": datetime.now()
            }
            
            # 更新偏好设置
            for key, value in preferences.items():
                if key in ['preferred_budget', 'preferred_activities']:
                    update_data[f"profile.{key}"] = value
                elif key == 'tags':
                    update_data['tags'] = value
            
            result = await db.users.update_one(
                {"user_id": user_id},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Failed to update user preferences for {user_id}: {str(e)}")
            return False
    
    # ==================== 行程记录管理 ====================
    
    async def save_itinerary(self, itinerary: TravelItinerary) -> bool:
        """保存行程记录"""
        try:
            db = await self._get_db()
            
            # 转换为MongoDB文档格式
            itinerary_data = {
                "trace_id": itinerary.trace_id,
                "user_id": itinerary.user_id,
                "created_at": itinerary.created_at,
                "summary": itinerary.summary.model_dump(),
                "daily_plans": [plan.model_dump() for plan in itinerary.daily_plans],
                "raw_user_query": itinerary.raw_user_query,
                "amap_links": itinerary.amap_links,
                "status": "completed"
            }
            
            result = await db.travel_itineraries.insert_one(itinerary_data)
            
            logger.info(f"Itinerary saved with ID: {result.inserted_id}")
            return result.acknowledged
            
        except Exception as e:
            logger.error(f"Failed to save itinerary {itinerary.trace_id}: {str(e)}")
            return False
    
    async def get_user_itineraries(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取用户的行程记录"""
        try:
            db = await self._get_db()
            
            cursor = db.travel_itineraries.find(
                {"user_id": user_id}
            ).sort("created_at", -1).limit(limit)
            
            itineraries = []
            async for doc in cursor:
                # 移除MongoDB的_id字段
                doc.pop('_id', None)
                itineraries.append(doc)
            
            return itineraries
            
        except Exception as e:
            logger.error(f"Failed to get itineraries for user {user_id}: {str(e)}")
            return []
    
    async def get_itinerary_by_trace_id(self, trace_id: str) -> Optional[Dict[str, Any]]:
        """根据trace_id获取行程"""
        try:
            db = await self._get_db()
            
            doc = await db.travel_itineraries.find_one({"trace_id": trace_id})
            if doc:
                doc.pop('_id', None)
                return doc
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get itinerary {trace_id}: {str(e)}")
            return None
    
    # ==================== 执行日志管理 ====================
    
    async def save_interaction_log(self, log_data: Dict[str, Any]) -> bool:
        """保存AI交互日志"""
        try:
            db = await self._get_db()
            
            # 确保必要字段存在
            log_data.setdefault('interaction_id', str(uuid.uuid4()))
            log_data.setdefault('timestamp', datetime.now())
            log_data.setdefault('status', 'PROCESSING')
            log_data.setdefault('application_source', 'travel_planner')
            
            result = await db.ai_interaction_logs.insert_one(log_data)
            
            logger.debug(f"Interaction log saved: {result.inserted_id}")
            return result.acknowledged
            
        except Exception as e:
            logger.error(f"Failed to save interaction log: {str(e)}")
            return False
    
    async def update_interaction_log(self, interaction_id: str, update_data: Dict[str, Any]) -> bool:
        """更新交互日志"""
        try:
            db = await self._get_db()
            
            update_data['updated_at'] = datetime.now()
            
            result = await db.ai_interaction_logs.update_one(
                {"interaction_id": interaction_id},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Failed to update interaction log {interaction_id}: {str(e)}")
            return False
    
    # ==================== 统计和分析 ====================
    
    async def get_user_statistics(self, user_id: str) -> Dict[str, Any]:
        """获取用户统计信息"""
        try:
            db = await self._get_db()
            
            # 统计行程数量
            itinerary_count = await db.travel_itineraries.count_documents({"user_id": user_id})
            
            # 统计最近30天的活动
            thirty_days_ago = datetime.now() - timedelta(days=30)
            recent_activity = await db.ai_interaction_logs.count_documents({
                "user_id": user_id,
                "timestamp": {"$gte": thirty_days_ago}
            })
            
            # 获取最常访问的目的地
            pipeline = [
                {"$match": {"user_id": user_id}},
                {"$group": {
                    "_id": "$summary.title",
                    "count": {"$sum": 1}
                }},
                {"$sort": {"count": -1}},
                {"$limit": 5}
            ]
            
            popular_destinations = []
            async for doc in db.travel_itineraries.aggregate(pipeline):
                popular_destinations.append({
                    "destination": doc["_id"],
                    "count": doc["count"]
                })
            
            return {
                "total_itineraries": itinerary_count,
                "recent_activity": recent_activity,
                "popular_destinations": popular_destinations,
                "last_activity": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get user statistics for {user_id}: {str(e)}")
            return {}


# 全局记忆管理器实例
memory_manager = MemoryManager()
