// 智能旅行规划助手 JavaScript 应用

class TravelPlannerApp {
    constructor() {
        this.currentSessionId = null;
        this.eventSource = null;
        this.isPlanning = false;
        
        this.initializeEventListeners();
        this.loadUserHistory();
    }

    initializeEventListeners() {
        // 规划表单提交
        document.getElementById('planningForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.startPlanning();
        });

        // 历史记录按钮
        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistory();
        });

        // 导出按钮
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportPlan();
        });

        // 分享按钮
        document.getElementById('shareBtn').addEventListener('click', () => {
            this.sharePlan();
        });
    }

    async startPlanning() {
        if (this.isPlanning) {
            this.showError('规划正在进行中，请稍候...');
            return;
        }

        const userId = document.getElementById('userId').value.trim();
        const query = document.getElementById('query').value.trim();
        const streamMode = document.getElementById('streamMode').checked;

        if (!userId || !query) {
            this.showError('请填写完整的用户ID和旅行需求');
            return;
        }

        this.isPlanning = true;
        this.hideError();
        this.hideWelcome();
        this.hideResult();

        try {
            if (streamMode) {
                await this.startStreamPlanning(userId, query);
            } else {
                await this.startSyncPlanning(userId, query);
            }
        } catch (error) {
            this.showError('规划失败: ' + error.message);
        } finally {
            this.isPlanning = false;
            this.updatePlanButton(false);
        }
    }

    async startStreamPlanning(userId, query) {
        this.updatePlanButton(true);
        this.showProgress();
        this.showThinking();

        // 启动流式规划
        const response = await fetch('/travel-planner/plan/stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                query: query,
                context: {}
            })
        });

        if (!response.ok) {
            throw new Error('启动流式规划失败');
        }

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.error || '启动流式规划失败');
        }

        this.currentSessionId = result.data.session_id;
        this.connectSSE();
    }

    async startSyncPlanning(userId, query) {
        this.updatePlanButton(true);
        this.showProgress();
        this.updateProgress(50, '正在规划中...');

        const response = await fetch('/travel-planner/plan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                query: query,
                context: {}
            })
        });

        if (!response.ok) {
            throw new Error('规划请求失败');
        }

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.error || '规划失败');
        }

        this.updateProgress(100, '规划完成');
        this.displayResult(result.data);
    }

    connectSSE() {
        if (this.eventSource) {
            this.eventSource.close();
        }

        this.eventSource = new EventSource(`/travel-planner/plan/stream/${this.currentSessionId}`);

        this.eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleSSEMessage(data);
            } catch (error) {
                console.error('解析SSE消息失败:', error);
            }
        };

        this.eventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            this.eventSource.close();
            this.showError('连接中断，请重试');
        };

        this.eventSource.addEventListener('connected', (event) => {
            console.log('SSE连接已建立');
        });

        this.eventSource.addEventListener('thinking', (event) => {
            const data = JSON.parse(event.data);
            this.addThinkingItem(data.data.content);
        });

        this.eventSource.addEventListener('progress', (event) => {
            const data = JSON.parse(event.data);
            const progress = Math.round(data.data.progress * 100);
            this.updateProgress(progress, data.data.step);
        });

        this.eventSource.addEventListener('complete', (event) => {
            const data = JSON.parse(event.data);
            this.displayResult(data.data);
            this.eventSource.close();
        });

        this.eventSource.addEventListener('error', (event) => {
            const data = JSON.parse(event.data);
            this.showError(data.data.error);
            this.eventSource.close();
        });
    }

    handleSSEMessage(data) {
        switch (data.event_type) {
            case 'thinking':
                this.addThinkingItem(data.data.content);
                break;
            case 'progress':
                const progress = Math.round(data.data.progress * 100);
                this.updateProgress(progress, data.data.step);
                break;
            case 'complete':
                this.displayResult(data.data.result);
                break;
            case 'error':
                this.showError(data.data.error);
                break;
        }
    }

    displayResult(data) {
        this.hideProgress();
        this.hideThinking();
        
        const resultCard = document.getElementById('resultCard');
        const itinerary = data.itinerary;
        
        // 显示行程概要
        this.displayTripSummary(itinerary.summary);
        
        // 显示每日行程
        this.displayDailyPlans(itinerary.daily_plans);
        
        // 显示地图链接
        this.displayMapLinks(itinerary.amap_links);
        
        resultCard.style.display = 'block';
    }

    displayTripSummary(summary) {
        const summaryHtml = `
            <div class="trip-summary">
                <h4><i class="bi bi-calendar-check"></i> ${summary.title}</h4>
                <p>${summary.suggestion || '为您精心规划的旅行行程'}</p>
                <div class="summary-stats">
                    <div class="stat-item">
                        <i class="bi bi-calendar3"></i>
                        <div>${summary.duration_days} 天</div>
                    </div>
                    <div class="stat-item">
                        <i class="bi bi-currency-dollar"></i>
                        <div>${summary.budget_estimate || '预算适中'}</div>
                    </div>
                    <div class="stat-item">
                        <i class="bi bi-geo-alt"></i>
                        <div>多个景点</div>
                    </div>
                </div>
            </div>
        `;
        document.getElementById('tripSummary').innerHTML = summaryHtml;
    }

    displayDailyPlans(dailyPlans) {
        const plansHtml = dailyPlans.map(plan => `
            <div class="day-plan">
                <div class="day-header">
                    <i class="bi bi-calendar-day"></i> 第${plan.day_number}天 - ${plan.theme}
                </div>
                <div class="day-content">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <small class="text-muted"><i class="bi bi-car-front"></i> 交通方式: ${plan.transportation}</small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted"><i class="bi bi-currency-dollar"></i> 预估费用: ¥${plan.estimated_cost}</small>
                        </div>
                    </div>
                    ${plan.pois.map(poi => this.createPOIItem(poi)).join('')}
                    ${plan.notes ? `<div class="alert alert-info mt-3"><i class="bi bi-info-circle"></i> ${plan.notes}</div>` : ''}
                </div>
            </div>
        `).join('');
        
        document.getElementById('dailyPlans').innerHTML = plansHtml;
    }

    createPOIItem(poi) {
        return `
            <div class="poi-item">
                <div class="poi-icon">
                    <i class="bi bi-geo-alt"></i>
                </div>
                <div class="poi-details">
                    <h6>${poi.name}</h6>
                    <div class="poi-address"><i class="bi bi-pin-map"></i> ${poi.address}</div>
                    <div class="poi-description">${poi.description}</div>
                    <div class="poi-tags">
                        ${poi.tags.map(tag => `<span class="poi-tag">${tag}</span>`).join('')}
                    </div>
                    <small class="text-muted">
                        <i class="bi bi-clock"></i> 建议游玩时间: ${poi.visit_duration}分钟
                        ${poi.recommended_time ? ` | 推荐时间: ${poi.recommended_time}` : ''}
                    </small>
                </div>
            </div>
        `;
    }

    displayMapLinks(mapLinks) {
        if (!mapLinks || Object.keys(mapLinks).length === 0) {
            document.getElementById('mapLinks').innerHTML = '';
            return;
        }

        const linksHtml = `
            <div class="map-links">
                <h6><i class="bi bi-map"></i> 高德地图导航</h6>
                <div class="mt-2">
                    ${Object.entries(mapLinks).map(([name, url]) => 
                        `<a href="${url}" target="_blank" class="map-link">
                            <i class="bi bi-navigation"></i> ${name}
                        </a>`
                    ).join('')}
                </div>
            </div>
        `;
        document.getElementById('mapLinks').innerHTML = linksHtml;
    }

    // UI 控制方法
    showProgress() {
        document.getElementById('progressCard').style.display = 'block';
        this.updateProgress(0, '准备开始...');
    }

    hideProgress() {
        document.getElementById('progressCard').style.display = 'none';
    }

    updateProgress(percent, step) {
        document.getElementById('progressBar').style.width = percent + '%';
        document.getElementById('currentStep').textContent = step;
    }

    showThinking() {
        document.getElementById('thinkingCard').style.display = 'block';
        document.getElementById('thinkingContent').innerHTML = '';
    }

    hideThinking() {
        document.getElementById('thinkingCard').style.display = 'none';
    }

    addThinkingItem(content) {
        const thinkingContent = document.getElementById('thinkingContent');
        const item = document.createElement('div');
        item.className = 'thinking-item';
        item.textContent = content;
        thinkingContent.appendChild(item);
        thinkingContent.scrollTop = thinkingContent.scrollHeight;
    }

    hideWelcome() {
        document.getElementById('welcomeCard').style.display = 'none';
    }

    hideResult() {
        document.getElementById('resultCard').style.display = 'none';
    }

    showError(message) {
        document.getElementById('errorMessage').textContent = message;
        document.getElementById('errorAlert').style.display = 'block';
        setTimeout(() => {
            this.hideError();
        }, 5000);
    }

    hideError() {
        document.getElementById('errorAlert').style.display = 'none';
    }

    updatePlanButton(isLoading) {
        const btn = document.getElementById('planBtn');
        if (isLoading) {
            btn.innerHTML = '<span class="loading-spinner"></span> 规划中...';
            btn.disabled = true;
        } else {
            btn.innerHTML = '<i class="bi bi-play-circle"></i> 开始规划';
            btn.disabled = false;
        }
    }

    // 历史记录相关方法
    async showHistory() {
        const userId = document.getElementById('userId').value.trim();
        if (!userId) {
            this.showError('请先输入用户ID');
            return;
        }

        const modal = new bootstrap.Modal(document.getElementById('historyModal'));
        modal.show();

        try {
            const response = await fetch(`/travel-planner/history/${userId}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayHistory(data.itineraries);
            } else {
                document.getElementById('historyContent').innerHTML = 
                    '<div class="text-center text-muted">暂无历史记录</div>';
            }
        } catch (error) {
            document.getElementById('historyContent').innerHTML = 
                '<div class="text-center text-danger">加载历史记录失败</div>';
        }
    }

    displayHistory(itineraries) {
        if (!itineraries || itineraries.length === 0) {
            document.getElementById('historyContent').innerHTML = 
                '<div class="text-center text-muted">暂无历史记录</div>';
            return;
        }

        const historyHtml = itineraries.map(item => `
            <div class="history-item" onclick="app.showDetail('${item.trace_id}')">
                <h6>${item.summary.title}</h6>
                <div class="history-meta">
                    <i class="bi bi-calendar"></i> ${new Date(item.created_at).toLocaleDateString()}
                    <span class="ms-3"><i class="bi bi-clock"></i> ${item.summary.duration_days}天</span>
                </div>
            </div>
        `).join('');

        document.getElementById('historyContent').innerHTML = historyHtml;
    }

    async showDetail(traceId) {
        try {
            const response = await fetch(`/travel-planner/plan/${traceId}`);
            const data = await response.json();
            
            if (data.success) {
                // 关闭历史记录模态框
                bootstrap.Modal.getInstance(document.getElementById('historyModal')).hide();
                
                // 显示详情模态框
                const modal = new bootstrap.Modal(document.getElementById('detailModal'));
                this.displayDetailContent(data.itinerary);
                modal.show();
            }
        } catch (error) {
            this.showError('加载详情失败');
        }
    }

    displayDetailContent(itinerary) {
        const content = `
            <div class="trip-summary">
                <h4>${itinerary.summary.title}</h4>
                <p>${itinerary.summary.suggestion || ''}</p>
            </div>
            ${itinerary.daily_plans.map(plan => `
                <div class="day-plan">
                    <div class="day-header">第${plan.day_number}天 - ${plan.theme}</div>
                    <div class="day-content">
                        ${plan.pois.map(poi => this.createPOIItem(poi)).join('')}
                    </div>
                </div>
            `).join('')}
        `;
        document.getElementById('detailContent').innerHTML = content;
    }

    // 导出和分享功能
    exportPlan() {
        // 实现导出功能
        this.showError('导出功能开发中...');
    }

    sharePlan() {
        // 实现分享功能
        if (navigator.share) {
            navigator.share({
                title: '我的旅行规划',
                text: '查看我的智能旅行规划',
                url: window.location.href
            });
        } else {
            // 复制链接到剪贴板
            navigator.clipboard.writeText(window.location.href).then(() => {
                this.showError('链接已复制到剪贴板');
            });
        }
    }

    async loadUserHistory() {
        // 页面加载时预加载用户历史（如果有默认用户ID）
        const userId = document.getElementById('userId').value.trim();
        if (userId) {
            // 可以在这里预加载一些用户数据
        }
    }
}

// 初始化应用
const app = new TravelPlannerApp();
