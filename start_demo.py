#!/usr/bin/env python3
"""
旅行规划Agent演示启动脚本

快速启动演示系统，展示旅行规划功能。
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.agents.travel_planner_agent import TravelPlannerAgent
from src.core.logger import get_logger
from src.core.database import db_manager

logger = get_logger('demo')


async def demo_travel_planning():
    """演示旅行规划功能"""
    print("🌟 AutoPilot AI - 智能旅行规划助手演示")
    print("=" * 50)
    
    try:
        # 初始化数据库连接
        print("📊 初始化数据库连接...")
        await db_manager.initialize()
        
        # 导入工具模块以注册工具
        print("🔧 注册工具...")
        import src.tools.amap_tools
        import src.tools.memory_tools
        
        # 创建旅行规划Agent
        print("🤖 创建旅行规划Agent...")
        agent = TravelPlannerAgent()
        
        # 初始化Agent状态
        state = agent.initialize_state(user_id="demo_user_001")
        print(f"✅ Agent初始化完成，追踪ID: {state.trace_id}")
        
        # 演示查询
        demo_queries = [
            "我想去北京玩3天，喜欢历史古迹和美食，预算中等",
            "计划上海2日游，偏爱现代建筑和购物",
            "想去杭州看西湖，1天行程，喜欢自然风光"
        ]
        
        print("\n🎯 可用的演示查询:")
        for i, query in enumerate(demo_queries, 1):
            print(f"  {i}. {query}")
        
        # 用户选择
        print("\n请选择一个查询进行演示 (1-3)，或输入自定义查询:")
        user_input = input(">>> ").strip()
        
        if user_input.isdigit() and 1 <= int(user_input) <= 3:
            selected_query = demo_queries[int(user_input) - 1]
        elif user_input:
            selected_query = user_input
        else:
            selected_query = demo_queries[0]  # 默认选择第一个
        
        print(f"\n🚀 开始规划: {selected_query}")
        print("-" * 50)
        
        # 模拟处理（由于没有真实的API密钥，我们创建一个模拟版本）
        await demo_mock_planning(agent, selected_query)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {str(e)}")
        logger.error(f"Demo failed: {str(e)}")
    
    finally:
        # 清理资源
        await db_manager.cleanup()
        print("\n✅ 演示完成，资源已清理")


async def demo_mock_planning(agent, query):
    """模拟旅行规划过程"""
    print("🧠 分析用户需求...")
    await asyncio.sleep(1)
    
    # 模拟意图分析
    mock_intent = {
        "destination": "北京" if "北京" in query else "上海" if "上海" in query else "杭州",
        "duration_days": 3 if "3天" in query else 2 if "2日" in query else 1,
        "travel_type": ["历史古迹", "美食"] if "历史" in query or "美食" in query else ["现代建筑", "购物"] if "现代" in query or "购物" in query else ["自然风光"],
        "budget": "medium",
        "summary": f"基于查询分析的{query[:20]}..."
    }
    
    print(f"✅ 意图分析完成:")
    print(f"   目的地: {mock_intent['destination']}")
    print(f"   天数: {mock_intent['duration_days']}天")
    print(f"   类型: {', '.join(mock_intent['travel_type'])}")
    print(f"   预算: {mock_intent['budget']}")
    
    print("\n🔍 收集相关信息...")
    await asyncio.sleep(1)
    
    # 模拟信息收集
    print("✅ 信息收集完成:")
    print("   - 地理位置信息已获取")
    print("   - POI搜索完成")
    print("   - 天气信息已查询")
    
    print("\n📋 规划行程...")
    await asyncio.sleep(2)
    
    # 模拟行程规划
    destination = mock_intent['destination']
    days = mock_intent['duration_days']
    
    print(f"✅ {destination}{days}日游行程规划完成:")
    
    for day in range(1, days + 1):
        print(f"\n   📅 第{day}天:")
        if destination == "北京":
            if day == 1:
                print("      🏛️ 故宫博物院 (上午)")
                print("      🍽️ 全聚德烤鸭 (中午)")
                print("      🌸 景山公园 (下午)")
            elif day == 2:
                print("      🏯 天坛公园 (上午)")
                print("      🛍️ 王府井大街 (下午)")
            elif day == 3:
                print("      🏔️ 八达岭长城 (全天)")
        elif destination == "上海":
            if day == 1:
                print("      🏙️ 外滩 (上午)")
                print("      🏢 陆家嘴 (下午)")
            elif day == 2:
                print("      🛍️ 南京路步行街 (上午)")
                print("      🎭 新天地 (下午)")
        else:  # 杭州
            print("      🌊 西湖风景区 (全天)")
            print("      🍃 雷峰塔 (下午)")
    
    print("\n🗺️ 高德地图导航链接已生成")
    print("💾 行程已保存到用户记录")
    
    print(f"\n🎉 规划完成！为您精心设计了{destination}{days}日游行程")


async def demo_api_info():
    """显示API信息"""
    print("\n📡 API接口信息:")
    print("   启动服务器: python run.py server")
    print("   访问地址: http://localhost:8000")
    print("   前端界面: http://localhost:8000/static/index.html")
    print("   API文档: http://localhost:8000/docs")
    
    print("\n🔧 主要API端点:")
    print("   POST /travel-planner/plan - 创建旅行规划")
    print("   POST /travel-planner/plan/stream - 启动流式规划")
    print("   GET  /travel-planner/plan/stream/{session_id} - SSE流")
    print("   GET  /travel-planner/history/{user_id} - 获取历史记录")


def main():
    """主函数"""
    print("AutoPilot AI - 智能旅行规划助手")
    print("选择演示模式:")
    print("1. 交互式规划演示")
    print("2. 显示API信息")
    print("3. 启动完整服务器")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        asyncio.run(demo_travel_planning())
    elif choice == "2":
        asyncio.run(demo_api_info())
    elif choice == "3":
        print("启动完整服务器...")
        import subprocess
        subprocess.run([sys.executable, "run.py", "server"])
    else:
        print("无效选择，启动交互式演示...")
        asyncio.run(demo_travel_planning())


if __name__ == "__main__":
    main()
