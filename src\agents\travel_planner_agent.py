"""
旅行规划Agent

基于高德地图API和LLM实现智能旅行规划功能。
"""
import json
import asyncio
from typing import Dict, List, Any, Optional, AsyncGenerator, Tuple
from datetime import datetime, timedelta
import yaml
from src.agents.base_agent import BaseAgent
from src.core.llm_manager import LLMManager
from src.models.travel_models import TravelRequest, TravelItinerary, DayPlan, POI, TripSummary
from src.tools.amap_tools import get_amap_tools_info
from src.core.logger import get_logger

logger = get_logger('agents.travel_planner')

# 加载配置
with open('config/default.yaml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)


class TravelPlannerAgent(BaseAgent):
    """旅行规划Agent"""
    
    def __init__(self):
        super().__init__(
            name="TravelPlannerAgent",
            description="智能旅行规划助手，提供个性化的旅行路线规划和推荐"
        )
        self.llm_manager = LLMManager()
        self.reasoning_llm = None
        self.basic_llm = None
        self._initialize_llms()
    
    def _initialize_llms(self):
        """初始化LLM实例"""
        try:
            # 推理LLM用于复杂决策
            reasoning_config = config.get('reasoning_llm', {})
            self.reasoning_llm = self.llm_manager.get_llm(
                model=reasoning_config.get('model'),
                api_key=reasoning_config.get('api_key'),
                base_url=reasoning_config.get('base_url')
            )
            
            # 基础LLM用于简单任务
            basic_config = config.get('basic_llm', {})
            self.basic_llm = self.llm_manager.get_llm(
                model=basic_config.get('model'),
                api_key=basic_config.get('api_key'),
                base_url=basic_config.get('base_url')
            )
            
            self.logger.info("LLM instances initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize LLMs: {str(e)}")
            raise
    
    async def process(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理旅行规划请求"""
        if not self._state:
            raise ValueError("Agent state not initialized")
        
        try:
            await self.update_status("thinking", "分析用户需求", 0.1)
            
            # 1. 意图理解和需求分析
            intent_analysis = await self._analyze_intent(query, context)
            await self.add_message("assistant", f"需求分析完成: {intent_analysis.get('summary', '')}")
            
            await self.update_status("executing", "搜索相关信息", 0.3)
            
            # 2. 信息收集
            collected_info = await self._collect_information(intent_analysis)
            
            await self.update_status("executing", "规划行程路线", 0.6)
            
            # 3. 行程规划
            itinerary = await self._plan_itinerary(intent_analysis, collected_info)
            
            await self.update_status("executing", "优化推荐结果", 0.8)
            
            # 4. 个性化优化
            optimized_itinerary = await self._optimize_itinerary(itinerary, context)
            
            await self.update_status("completed", "规划完成", 1.0)
            
            result = {
                "trace_id": self._state.trace_id,
                "status": "success",
                "itinerary": optimized_itinerary.model_dump(),
                "intent_analysis": intent_analysis,
                "tools_used": self._state.tools_used
            }
            
            await self.add_message("assistant", "旅行规划已完成！")
            return result
            
        except Exception as e:
            await self.update_status("failed", f"规划失败: {str(e)}", 0.0)
            self.logger.error(f"Travel planning failed: {str(e)}")
            raise
    
    async def stream_process(self, query: str, context: Dict[str, Any] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """流式处理旅行规划请求"""
        if not self._state:
            raise ValueError("Agent state not initialized")
        
        try:
            # 1. 意图理解
            yield {"step": "intent_analysis", "status": "started", "progress": 0.1}
            intent_analysis = await self._analyze_intent(query, context)
            yield {"step": "intent_analysis", "status": "completed", "data": intent_analysis, "progress": 0.2}
            
            # 2. 信息收集
            yield {"step": "information_collection", "status": "started", "progress": 0.3}
            async for info_chunk in self._stream_collect_information(intent_analysis):
                yield {"step": "information_collection", "status": "progress", "data": info_chunk, "progress": 0.5}
            
            # 3. 行程规划
            yield {"step": "itinerary_planning", "status": "started", "progress": 0.6}
            collected_info = await self._collect_information(intent_analysis)
            itinerary = await self._plan_itinerary(intent_analysis, collected_info)
            yield {"step": "itinerary_planning", "status": "completed", "data": itinerary.model_dump(), "progress": 0.8}

            # 4. 个性化优化
            yield {"step": "optimization", "status": "started", "progress": 0.9}
            optimized_itinerary = await self._optimize_itinerary(itinerary, context)
            yield {"step": "optimization", "status": "completed", "data": optimized_itinerary.model_dump(), "progress": 1.0}

            # 最终结果
            yield {
                "step": "completed",
                "status": "success",
                "data": {
                    "trace_id": self._state.trace_id,
                    "itinerary": optimized_itinerary.model_dump(),
                    "intent_analysis": intent_analysis
                },
                "progress": 1.0
            }
            
        except Exception as e:
            yield {"step": "error", "status": "failed", "error": str(e), "progress": 0.0}
            raise
    
    async def _analyze_intent(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """分析用户意图"""
        prompt = f"""
        分析以下旅行查询的意图和需求：
        
        用户查询: {query}
        上下文信息: {json.dumps(context or {}, ensure_ascii=False)}
        
        请提取以下信息：
        1. 目的地城市/地区
        2. 旅行时间（开始日期、天数）
        3. 旅行类型（观光、美食、购物、亲子等）
        4. 预算范围
        5. 交通方式偏好
        6. 特殊需求
        
        以JSON格式返回分析结果。
        """
        
        response = await self.reasoning_llm.agenerate([prompt])
        try:
            # 解析LLM响应
            content = response.generations[0][0].text.strip()
            # 尝试提取JSON部分
            if '```json' in content:
                json_start = content.find('```json') + 7
                json_end = content.find('```', json_start)
                content = content[json_start:json_end].strip()
            
            return json.loads(content)
        except Exception as e:
            self.logger.warning(f"Failed to parse intent analysis: {str(e)}")
            return {
                "destination": "未指定",
                "duration_days": 1,
                "travel_type": ["观光"],
                "budget": "medium",
                "transport": "driving",
                "summary": query
            }
    
    async def _collect_information(self, intent_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """收集相关信息"""
        destination = intent_analysis.get('destination', '')
        travel_type = intent_analysis.get('travel_type', [])
        
        collected_info = {
            "pois": [],
            "routes": [],
            "weather": None,
            "location_info": None
        }
        
        try:
            # 地理编码获取目的地坐标
            if destination:
                location_result = await self.use_tool('geocode', address=destination)
                collected_info["location_info"] = location_result
            
            # 搜索POI
            for poi_type in travel_type:
                poi_result = await self.use_tool('search_poi', 
                                               keywords=poi_type, 
                                               city=destination,
                                               offset=10)
                collected_info["pois"].extend(poi_result.get('pois', []))
            
            # 获取天气信息
            if destination:
                weather_result = await self.use_tool('weather_info', city=destination)
                collected_info["weather"] = weather_result
            
        except Exception as e:
            self.logger.error(f"Information collection failed: {str(e)}")
        
        return collected_info

    async def _stream_collect_information(self, intent_analysis: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """流式收集信息"""
        destination = intent_analysis.get('destination', '')
        travel_type = intent_analysis.get('travel_type', [])

        # 地理编码
        if destination:
            yield {"type": "geocoding", "status": "started", "target": destination}
            location_result = await self.use_tool('geocode', address=destination)
            yield {"type": "geocoding", "status": "completed", "data": location_result}

        # POI搜索
        for poi_type in travel_type:
            yield {"type": "poi_search", "status": "started", "target": poi_type}
            poi_result = await self.use_tool('search_poi',
                                           keywords=poi_type,
                                           city=destination,
                                           offset=10)
            yield {"type": "poi_search", "status": "completed", "data": poi_result}

        # 天气信息
        if destination:
            yield {"type": "weather", "status": "started", "target": destination}
            weather_result = await self.use_tool('weather_info', city=destination)
            yield {"type": "weather", "status": "completed", "data": weather_result}

    async def _plan_itinerary(self, intent_analysis: Dict[str, Any], collected_info: Dict[str, Any]) -> TravelItinerary:
        """规划行程"""
        destination = intent_analysis.get('destination', '未知目的地')
        duration_days = intent_analysis.get('duration_days', 1)

        # 构建行程规划提示词
        prompt = f"""
        基于以下信息规划{duration_days}天的旅行行程：

        目的地: {destination}
        旅行类型: {intent_analysis.get('travel_type', [])}
        预算: {intent_analysis.get('budget', 'medium')}

        可用POI信息:
        {json.dumps(collected_info.get('pois', [])[:20], ensure_ascii=False, indent=2)}

        天气信息:
        {json.dumps(collected_info.get('weather', {}), ensure_ascii=False, indent=2)}

        请规划详细的每日行程，包括：
        1. 每日主题和重点活动
        2. 具体的POI推荐和时间安排
        3. 交通路线建议
        4. 用餐推荐
        5. 预算估算

        以结构化JSON格式返回。
        """

        response = await self.reasoning_llm.agenerate([prompt])

        try:
            # 解析规划结果
            content = response.generations[0][0].text.strip()
            if '```json' in content:
                json_start = content.find('```json') + 7
                json_end = content.find('```', json_start)
                content = content[json_start:json_end].strip()

            plan_data = json.loads(content)

            # 构建TravelItinerary对象
            daily_plans = []
            for day_data in plan_data.get('daily_plans', []):
                pois = []
                for poi_data in day_data.get('pois', []):
                    poi = POI(
                        name=poi_data.get('name', ''),
                        address=poi_data.get('address', ''),
                        location=poi_data.get('location', ''),
                        poi_type=poi_data.get('type', ''),
                        rating=poi_data.get('rating', 0.0),
                        description=poi_data.get('description', ''),
                        visit_duration=poi_data.get('visit_duration', 60),
                        recommended_time=poi_data.get('recommended_time', ''),
                        tags=poi_data.get('tags', [])
                    )
                    pois.append(poi)

                day_plan = DayPlan(
                    day_number=day_data.get('day_number', 1),
                    theme=day_data.get('theme', ''),
                    pois=pois,
                    route_summary=day_data.get('route_summary', ''),
                    transportation=day_data.get('transportation', ''),
                    estimated_cost=day_data.get('estimated_cost', 0.0),
                    notes=day_data.get('notes', '')
                )
                daily_plans.append(day_plan)

            summary = TripSummary(
                title=plan_data.get('title', f'{destination}{duration_days}日游'),
                suggestion=plan_data.get('suggestion', ''),
                duration_days=duration_days,
                budget_estimate=plan_data.get('budget_estimate', '')
            )

            itinerary = TravelItinerary(
                trace_id=self._state.trace_id,
                user_id=self._state.user_id,
                summary=summary,
                daily_plans=daily_plans,
                raw_user_query=intent_analysis.get('original_query', ''),
                amap_links={}
            )

            return itinerary

        except Exception as e:
            self.logger.error(f"Failed to parse itinerary plan: {str(e)}")
            # 返回默认行程
            return self._create_default_itinerary(intent_analysis)

    def _create_default_itinerary(self, intent_analysis: Dict[str, Any]) -> TravelItinerary:
        """创建默认行程"""
        destination = intent_analysis.get('destination', '未知目的地')
        duration_days = intent_analysis.get('duration_days', 1)

        # 创建简单的默认行程
        daily_plans = []
        for day in range(1, duration_days + 1):
            day_plan = DayPlan(
                day_number=day,
                theme=f"第{day}天 - 探索{destination}",
                pois=[],
                route_summary="待规划",
                transportation="步行/公共交通",
                estimated_cost=200.0,
                notes="请根据具体需求调整行程"
            )
            daily_plans.append(day_plan)

        summary = TripSummary(
            title=f'{destination}{duration_days}日游',
            suggestion='基础行程规划，建议根据个人喜好调整',
            duration_days=duration_days,
            budget_estimate='中等预算'
        )

        return TravelItinerary(
            trace_id=self._state.trace_id,
            user_id=self._state.user_id,
            summary=summary,
            daily_plans=daily_plans,
            raw_user_query=intent_analysis.get('original_query', ''),
            amap_links={}
        )

    async def _optimize_itinerary(self, itinerary: TravelItinerary, context: Dict[str, Any] = None) -> TravelItinerary:
        """个性化优化行程"""
        # 这里可以基于用户画像和历史数据进行个性化优化
        # 目前返回原始行程，后续可以扩展
        _ = context  # 避免未使用变量警告

        # 添加高德地图链接
        amap_links = {}
        for day_plan in itinerary.daily_plans:
            for poi in day_plan.pois:
                if poi.location:
                    # 生成高德地图链接
                    amap_url = f"https://uri.amap.com/marker?position={poi.location}&name={poi.name}"
                    amap_links[poi.name] = amap_url

        itinerary.amap_links = amap_links
        return itinerary
