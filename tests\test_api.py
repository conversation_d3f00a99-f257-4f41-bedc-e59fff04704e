"""
API接口测试用例
"""
import pytest
import json
from unittest.mock import patch, AsyncMock
from httpx import AsyncClient


class TestTravelPlannerAPI:
    """旅行规划API测试类"""
    
    @pytest.mark.asyncio
    async def test_root_endpoint(self, async_client: AsyncClient):
        """测试根路径"""
        response = await async_client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "docs" in data
        assert "frontend" in data
    
    @pytest.mark.asyncio
    async def test_health_check(self, async_client: AsyncClient):
        """测试健康检查"""
        response = await async_client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "message" in data
        assert "database" in data
    
    @pytest.mark.asyncio
    async def test_create_travel_plan_success(self, async_client: AsyncClient):
        """测试创建旅行规划成功"""
        request_data = {
            "user_id": "test_user_001",
            "query": "我想去北京玩3天，喜欢历史古迹和美食",
            "context": {}
        }
        
        # 模拟Agent处理成功
        mock_result = {
            "status": "success",
            "itinerary": {
                "trace_id": "test_trace_001",
                "user_id": "test_user_001",
                "summary": {
                    "title": "北京3日游",
                    "duration_days": 3,
                    "budget_estimate": "中等预算"
                },
                "daily_plans": [],
                "raw_user_query": request_data["query"],
                "amap_links": {}
            },
            "intent_analysis": {
                "destination": "北京",
                "duration_days": 3
            },
            "tools_used": ["search_poi", "weather_info"]
        }
        
        with patch('src.api.travel_planner.router.TravelPlannerAgent') as mock_agent_class:
            mock_agent = AsyncMock()
            mock_agent.initialize_state.return_value.trace_id = "test_trace_001"
            mock_agent.process.return_value = mock_result
            mock_agent_class.return_value = mock_agent
            
            response = await async_client.post("/travel-planner/plan", json=request_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["trace_id"] == "test_trace_001"
            assert "data" in data
            assert data["data"]["status"] == "success"
    
    @pytest.mark.asyncio
    async def test_create_travel_plan_failure(self, async_client: AsyncClient):
        """测试创建旅行规划失败"""
        request_data = {
            "user_id": "test_user_001",
            "query": "测试查询",
            "context": {}
        }
        
        with patch('src.api.travel_planner.router.TravelPlannerAgent') as mock_agent_class:
            mock_agent = AsyncMock()
            mock_agent.initialize_state.return_value.trace_id = "test_trace_001"
            mock_agent.process.side_effect = Exception("处理失败")
            mock_agent_class.return_value = mock_agent
            
            response = await async_client.post("/travel-planner/plan", json=request_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is False
            assert "error" in data
    
    @pytest.mark.asyncio
    async def test_start_stream_planning(self, async_client: AsyncClient):
        """测试启动流式规划"""
        request_data = {
            "user_id": "test_user_001",
            "query": "我想去上海玩2天",
            "session_id": "test_session_001",
            "context": {}
        }
        
        response = await async_client.post("/travel-planner/plan/stream", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "session_id" in data["data"]
    
    @pytest.mark.asyncio
    async def test_stream_travel_plan(self, async_client: AsyncClient):
        """测试SSE流式接口"""
        session_id = "test_session_001"
        
        response = await async_client.get(f"/travel-planner/plan/stream/{session_id}")
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
    
    @pytest.mark.asyncio
    async def test_get_user_travel_history_empty(self, async_client: AsyncClient):
        """测试获取用户旅行历史（空结果）"""
        user_id = "test_user_empty"
        
        with patch('src.api.travel_planner.router.memory_manager') as mock_memory:
            mock_memory.get_user_itineraries.return_value = []
            
            response = await async_client.get(f"/travel-planner/history/{user_id}")
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["user_id"] == user_id
            assert data["itineraries"] == []
            assert data["count"] == 0
    
    @pytest.mark.asyncio
    async def test_get_user_travel_history_with_data(self, async_client: AsyncClient):
        """测试获取用户旅行历史（有数据）"""
        user_id = "test_user_001"
        mock_itineraries = [
            {
                "trace_id": "trace_001",
                "user_id": user_id,
                "summary": {
                    "title": "北京3日游",
                    "duration_days": 3
                },
                "created_at": "2024-06-18T10:00:00"
            }
        ]
        
        with patch('src.api.travel_planner.router.memory_manager') as mock_memory:
            mock_memory.get_user_itineraries.return_value = mock_itineraries
            
            response = await async_client.get(f"/travel-planner/history/{user_id}")
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["count"] == 1
            assert len(data["itineraries"]) == 1
            assert data["itineraries"][0]["trace_id"] == "trace_001"
    
    @pytest.mark.asyncio
    async def test_get_travel_plan_success(self, async_client: AsyncClient):
        """测试获取旅行规划成功"""
        trace_id = "test_trace_001"
        mock_itinerary = {
            "trace_id": trace_id,
            "user_id": "test_user_001",
            "summary": {
                "title": "北京3日游",
                "duration_days": 3
            },
            "daily_plans": []
        }
        
        with patch('src.api.travel_planner.router.memory_manager') as mock_memory:
            mock_memory.get_itinerary_by_trace_id.return_value = mock_itinerary
            
            response = await async_client.get(f"/travel-planner/plan/{trace_id}")
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["itinerary"]["trace_id"] == trace_id
    
    @pytest.mark.asyncio
    async def test_get_travel_plan_not_found(self, async_client: AsyncClient):
        """测试获取不存在的旅行规划"""
        trace_id = "nonexistent_trace"
        
        with patch('src.api.travel_planner.router.memory_manager') as mock_memory:
            mock_memory.get_itinerary_by_trace_id.return_value = None
            
            response = await async_client.get(f"/travel-planner/plan/{trace_id}")
            
            assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_get_user_profile_success(self, async_client: AsyncClient):
        """测试获取用户画像成功"""
        user_id = "test_user_001"
        
        from src.models.travel_models import User, UserProfile
        mock_user = User(
            user_id=user_id,
            profile=UserProfile(
                name="测试用户",
                preferred_budget="medium",
                preferred_activities=["美食"],
                tags=["自驾游"]
            )
        )
        
        with patch('src.api.travel_planner.router.memory_manager') as mock_memory:
            mock_memory.get_user_profile.return_value = mock_user
            
            response = await async_client.get(f"/travel-planner/user/{user_id}/profile")
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["profile"]["user_id"] == user_id
            assert data["profile"]["profile"]["name"] == "测试用户"
    
    @pytest.mark.asyncio
    async def test_get_user_profile_not_found(self, async_client: AsyncClient):
        """测试获取不存在的用户画像"""
        user_id = "nonexistent_user"
        
        with patch('src.api.travel_planner.router.memory_manager') as mock_memory:
            mock_memory.get_user_profile.return_value = None
            
            response = await async_client.get(f"/travel-planner/user/{user_id}/profile")
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is False
            assert data["profile"] is None
    
    @pytest.mark.asyncio
    async def test_get_user_statistics(self, async_client: AsyncClient):
        """测试获取用户统计信息"""
        user_id = "test_user_001"
        mock_stats = {
            "total_itineraries": 5,
            "recent_activity": 3,
            "popular_destinations": [
                {"destination": "北京", "count": 2},
                {"destination": "上海", "count": 1}
            ]
        }
        
        with patch('src.api.travel_planner.router.memory_manager') as mock_memory:
            mock_memory.get_user_statistics.return_value = mock_stats
            
            response = await async_client.get(f"/travel-planner/user/{user_id}/statistics")
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["user_id"] == user_id
            assert data["statistics"]["total_itineraries"] == 5
    
    @pytest.mark.asyncio
    async def test_invalid_request_data(self, async_client: AsyncClient):
        """测试无效请求数据"""
        # 缺少必需字段
        invalid_data = {
            "query": "测试查询"
            # 缺少 user_id
        }
        
        response = await async_client.post("/travel-planner/plan", json=invalid_data)
        
        assert response.status_code == 422  # Validation Error
    
    @pytest.mark.asyncio
    async def test_api_error_handling(self, async_client: AsyncClient):
        """测试API错误处理"""
        request_data = {
            "user_id": "test_user_001",
            "query": "测试查询",
            "context": {}
        }
        
        with patch('src.api.travel_planner.router.memory_manager') as mock_memory:
            mock_memory.get_user_itineraries.side_effect = Exception("数据库错误")
            
            response = await async_client.get("/travel-planner/history/test_user_001")
            
            assert response.status_code == 500
