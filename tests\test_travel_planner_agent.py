"""
旅行规划Agent测试用例
"""
import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from src.agents.travel_planner_agent import TravelPlannerAgent
from src.models.travel_models import TravelItinerary


class TestTravelPlannerAgent:
    """旅行规划Agent测试类"""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self):
        """测试Agent初始化"""
        agent = TravelPlannerAgent()
        
        assert agent.name == "TravelPlannerAgent"
        assert agent.description is not None
        assert agent.reasoning_llm is not None
        assert agent.basic_llm is not None
    
    @pytest.mark.asyncio
    async def test_state_initialization(self):
        """测试状态初始化"""
        agent = TravelPlannerAgent()
        state = agent.initialize_state(user_id="test_user_001", session_id="test_session")
        
        assert state.user_id == "test_user_001"
        assert state.session_id == "test_session"
        assert state.status == "initialized"
        assert state.trace_id is not None
        assert len(state.messages) == 0
        assert len(state.tools_used) == 0
    
    @pytest.mark.asyncio
    async def test_analyze_intent(self, travel_agent, sample_travel_query, mock_llm_intent_response):
        """测试意图分析"""
        with patch.object(travel_agent.reasoning_llm, 'agenerate', return_value=mock_llm_intent_response):
            result = await travel_agent._analyze_intent(sample_travel_query)
            
            assert isinstance(result, dict)
            assert "destination" in result
            assert "duration_days" in result
            assert "travel_type" in result
            assert result["destination"] == "北京"
            assert result["duration_days"] == 3
    
    @pytest.mark.asyncio
    async def test_analyze_intent_fallback(self, travel_agent, sample_travel_query):
        """测试意图分析失败时的回退机制"""
        # 模拟LLM返回无效JSON
        mock_response = MagicMock()
        mock_response.generations = [[MagicMock(text="invalid json response")]]
        
        with patch.object(travel_agent.reasoning_llm, 'agenerate', return_value=mock_response):
            result = await travel_agent._analyze_intent(sample_travel_query)
            
            assert isinstance(result, dict)
            assert "destination" in result
            assert "summary" in result
            assert result["destination"] == "未指定"
    
    @pytest.mark.asyncio
    async def test_collect_information(self, travel_agent, mock_amap_response, mock_weather_response):
        """测试信息收集"""
        intent_analysis = {
            "destination": "北京",
            "travel_type": ["历史古迹", "美食"]
        }
        
        # 模拟工具调用
        with patch.object(travel_agent, 'use_tool') as mock_use_tool:
            mock_use_tool.side_effect = [
                {"geocodes": [{"location": "116.397477,39.903738"}]},  # geocode
                mock_amap_response,  # search_poi for 历史古迹
                mock_amap_response,  # search_poi for 美食
                mock_weather_response  # weather_info
            ]
            
            result = await travel_agent._collect_information(intent_analysis)
            
            assert isinstance(result, dict)
            assert "pois" in result
            assert "weather" in result
            assert "location_info" in result
            assert len(result["pois"]) > 0
    
    @pytest.mark.asyncio
    async def test_plan_itinerary(self, travel_agent, mock_llm_planning_response):
        """测试行程规划"""
        intent_analysis = {
            "destination": "北京",
            "duration_days": 3,
            "travel_type": ["历史古迹"],
            "budget": "medium"
        }
        
        collected_info = {
            "pois": [
                {
                    "name": "故宫博物院",
                    "address": "北京市东城区景山前街4号",
                    "location": "116.397026,39.918058"
                }
            ],
            "weather": {"weather": "晴"},
            "location_info": {"location": "116.397477,39.903738"}
        }
        
        with patch.object(travel_agent.reasoning_llm, 'agenerate', return_value=mock_llm_planning_response):
            result = await travel_agent._plan_itinerary(intent_analysis, collected_info)
            
            assert isinstance(result, TravelItinerary)
            assert result.summary.title is not None
            assert result.summary.duration_days == 3
            assert len(result.daily_plans) > 0
            assert result.trace_id == travel_agent._state.trace_id
    
    @pytest.mark.asyncio
    async def test_create_default_itinerary(self, travel_agent):
        """测试创建默认行程"""
        intent_analysis = {
            "destination": "北京",
            "duration_days": 2,
            "original_query": "北京2日游"
        }
        
        result = travel_agent._create_default_itinerary(intent_analysis)
        
        assert isinstance(result, TravelItinerary)
        assert result.summary.title == "北京2日游"
        assert result.summary.duration_days == 2
        assert len(result.daily_plans) == 2
        assert all(plan.day_number in [1, 2] for plan in result.daily_plans)
    
    @pytest.mark.asyncio
    async def test_optimize_itinerary(self, travel_agent):
        """测试行程优化"""
        # 创建测试行程
        intent_analysis = {"destination": "北京", "duration_days": 1}
        itinerary = travel_agent._create_default_itinerary(intent_analysis)
        
        # 添加一些POI用于测试地图链接生成
        from src.models.travel_models import POI
        poi = POI(
            name="故宫博物院",
            address="北京市东城区景山前街4号",
            location="116.397026,39.918058",
            poi_type="历史古迹"
        )
        itinerary.daily_plans[0].pois = [poi]
        
        result = await travel_agent._optimize_itinerary(itinerary)
        
        assert isinstance(result, TravelItinerary)
        assert len(result.amap_links) > 0
        assert "故宫博物院" in result.amap_links
        assert "https://uri.amap.com/marker" in result.amap_links["故宫博物院"]
    
    @pytest.mark.asyncio
    async def test_process_complete_flow(self, travel_agent, sample_travel_query, 
                                       mock_llm_intent_response, mock_llm_planning_response,
                                       mock_amap_response, mock_weather_response):
        """测试完整的处理流程"""
        # 模拟所有依赖
        with patch.object(travel_agent.reasoning_llm, 'agenerate') as mock_reasoning_llm, \
             patch.object(travel_agent, 'use_tool') as mock_use_tool:
            
            # 设置LLM响应
            mock_reasoning_llm.side_effect = [mock_llm_intent_response, mock_llm_planning_response]
            
            # 设置工具调用响应
            mock_use_tool.side_effect = [
                {"geocodes": [{"location": "116.397477,39.903738"}]},  # geocode
                mock_amap_response,  # search_poi
                mock_weather_response  # weather_info
            ]
            
            result = await travel_agent.process(sample_travel_query)
            
            assert isinstance(result, dict)
            assert result["status"] == "success"
            assert "itinerary" in result
            assert "intent_analysis" in result
            assert "tools_used" in result
            assert result["trace_id"] == travel_agent._state.trace_id
    
    @pytest.mark.asyncio
    async def test_stream_process(self, travel_agent, sample_travel_query,
                                mock_llm_intent_response, mock_llm_planning_response,
                                mock_amap_response, mock_weather_response):
        """测试流式处理"""
        # 模拟所有依赖
        with patch.object(travel_agent.reasoning_llm, 'agenerate') as mock_reasoning_llm, \
             patch.object(travel_agent, 'use_tool') as mock_use_tool:
            
            mock_reasoning_llm.side_effect = [mock_llm_intent_response, mock_llm_planning_response]
            mock_use_tool.side_effect = [
                {"geocodes": [{"location": "116.397477,39.903738"}]},
                mock_amap_response,
                mock_weather_response
            ]
            
            results = []
            async for result in travel_agent.stream_process(sample_travel_query):
                results.append(result)
            
            # 验证流式输出的步骤
            steps = [r["step"] for r in results]
            assert "intent_analysis" in steps
            assert "information_collection" in steps
            assert "itinerary_planning" in steps
            assert "optimization" in steps
            assert "completed" in steps
            
            # 验证最终结果
            final_result = next(r for r in results if r["step"] == "completed")
            assert final_result["status"] == "success"
            assert "itinerary" in final_result["data"]
    
    @pytest.mark.asyncio
    async def test_error_handling(self, travel_agent, sample_travel_query):
        """测试错误处理"""
        # 模拟LLM调用失败
        with patch.object(travel_agent.reasoning_llm, 'agenerate', side_effect=Exception("LLM Error")):
            with pytest.raises(Exception):
                await travel_agent.process(sample_travel_query)
            
            # 验证状态更新
            assert travel_agent._state.status == "failed"
    
    @pytest.mark.asyncio
    async def test_tool_usage_tracking(self, travel_agent, mock_amap_response):
        """测试工具使用跟踪"""
        with patch.object(travel_agent, 'use_tool', return_value=mock_amap_response) as mock_use_tool:
            await travel_agent.use_tool('search_poi', keywords='测试')
            
            assert 'search_poi' in travel_agent._state.tools_used
            mock_use_tool.assert_called_once_with('search_poi', keywords='测试')
    
    @pytest.mark.asyncio
    async def test_message_tracking(self, travel_agent):
        """测试消息跟踪"""
        await travel_agent.add_message("user", "测试消息")
        await travel_agent.add_message("assistant", "回复消息")
        
        assert len(travel_agent._state.messages) == 2
        assert travel_agent._state.messages[0]["role"] == "user"
        assert travel_agent._state.messages[0]["content"] == "测试消息"
        assert travel_agent._state.messages[1]["role"] == "assistant"
        assert travel_agent._state.messages[1]["content"] == "回复消息"
    
    @pytest.mark.asyncio
    async def test_status_updates(self, travel_agent):
        """测试状态更新"""
        await travel_agent.update_status("thinking", "分析需求", 0.2)
        
        assert travel_agent._state.status == "thinking"
        assert travel_agent._state.current_step == "分析需求"
        assert travel_agent._state.progress == 0.2
    
    @pytest.mark.asyncio
    async def test_cleanup(self, travel_agent):
        """测试资源清理"""
        await travel_agent.cleanup()
        # 验证清理操作完成（这里主要是确保没有异常）
        assert True
